#!/usr/bin/env python3
"""
ThePalmTree Master Strategy
An advanced trading strategy with enhanced machine learning capabilities,
self-learning mechanisms, and session awareness,
optimized for high win rate across all market conditions.
"""

import json
import logging
import os
import random
import time
from datetime import datetime, timedelta
from functools import reduce
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import pytz
import talib.abstract as ta
from freqtrade.strategy import (
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
)
from pandas import DataFrame

# Set up logger
logger = logging.getLogger(__name__)

# Try to import ML dependencies
try:
    # Enhanced ML imports
    import joblib
    from sklearn.ensemble import (
        GradientBoostingClassifier,
        RandomForestClassifier,
        VotingClassifier,
    )
    from sklearn.linear_model import LogisticRegression
    from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
    from sklearn.model_selection import train_test_split
    from sklearn.neural_network import MLPClassifier
    from sklearn.pipeline import Pipeline
    from sklearn.preprocessing import StandardScaler

    ML_DEPENDENCIES_AVAILABLE = True
except ImportError:
    ML_DEPENDENCIES_AVAILABLE = False
    logger.warning(
        "ML dependencies not available. Enhanced ML features will be disabled."
    )


class MLEnsembleModel:
    """
    Enhanced ML model with ensemble methods and multiple model types.
    """

    def __init__(self, model_dir: str = "/tmp/ml_models"):
        """
        Initialize the ML ensemble model.
        """
        if not ML_DEPENDENCIES_AVAILABLE:
            logger.warning(
                "ML dependencies not available. MLEnsembleModel will not function."
            )
            return

        self.model_dir = model_dir
        self.models = {}
        self.ensemble = None
        self.feature_importance = {}
        self.confidence_intervals = {}
        self.last_training_time = {}
        self.model_performance = {}

        # Create model directory if it doesn't exist
        os.makedirs(model_dir, exist_ok=True)

    def prepare_features(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare features for ML model
        """
        if not ML_DEPENDENCIES_AVAILABLE:
            return pd.DataFrame()

        # Technical indicators as features
        features = dataframe[
            [
                "rsi",
                "adx",
                "cci",
                "mfi",
                "macd",
                "macdsignal",
                "bb_lowerband",
                "bb_middleband",
                "bb_upperband",
                "ema_short",
                "ema_long",
                "volume_mean_12",
                "volume_mean_24",
            ]
        ].copy()

        # Add price action features
        features["close_change"] = dataframe["close"].pct_change()
        features["high_low_range"] = (dataframe["high"] - dataframe["low"]) / dataframe[
            "low"
        ]
        features["body_size"] = (
            abs(dataframe["close"] - dataframe["open"]) / dataframe["open"]
        )
        features["upper_wick"] = (
            dataframe["high"] - dataframe[["open", "close"]].max(axis=1)
        ) / dataframe["open"]
        features["lower_wick"] = (
            dataframe[["open", "close"]].min(axis=1) - dataframe["low"]
        ) / dataframe["open"]

        # Add volatility features
        features["atr"] = dataframe["atr"]
        features["atr_percent"] = dataframe["atr"] / dataframe["close"]

        # Add volume features
        features["volume_change"] = dataframe["volume"].pct_change()
        features["volume_relative"] = dataframe["volume"] / features["volume_mean_24"]

        # Handle NaN values
        features = features.fillna(0)

        return features

    def prepare_labels(
        self, dataframe: pd.DataFrame, lookahead: int = 12, threshold: float = 0.01
    ) -> np.ndarray:
        """
        Prepare labels for ML model based on future price movement
        """
        # Validate inputs
        if not isinstance(dataframe, pd.DataFrame) or dataframe.empty:
            return np.array([])

        # Calculate future returns
        future_returns = dataframe["close"].shift(-lookahead) / dataframe["close"] - 1

        # Create labels: 1 for up, 0 for down
        labels = (future_returns > threshold).astype(int)

        return labels.fillna(0).values

    def train_models(
        self, dataframe: pd.DataFrame, pair: str, force_retrain: bool = False
    ) -> bool:
        """
        Train multiple ML models and create an ensemble
        """
        if not ML_DEPENDENCIES_AVAILABLE:
            return False

        # Check if we need to retrain (once per day)
        current_time = datetime.now()
        if pair in self.last_training_time and not force_retrain:
            last_train = self.last_training_time[pair]
            if (current_time - last_train).total_seconds() < 86400:  # 24 hours
                logger.info(
                    f"Skipping training for {pair}, last trained {(current_time - last_train).total_seconds() / 3600:.1f} hours ago"
                )
                return False

        # Prepare features and labels
        features = self.prepare_features(dataframe)
        labels = self.prepare_labels(dataframe)

        if len(features) == 0 or len(labels) == 0:
            logger.warning(f"No features or labels available for {pair}")
            return False

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            features, labels, test_size=0.2, shuffle=False
        )

        # Create and train individual models
        models = {
            "random_forest": Pipeline(
                [
                    ("scaler", StandardScaler()),
                    (
                        "model",
                        RandomForestClassifier(
                            n_estimators=100, max_depth=5, random_state=42
                        ),
                    ),
                ]
            ),
            "gradient_boost": Pipeline(
                [
                    ("scaler", StandardScaler()),
                    (
                        "model",
                        GradientBoostingClassifier(
                            n_estimators=100, max_depth=3, random_state=42
                        ),
                    ),
                ]
            ),
            "logistic": Pipeline(
                [
                    ("scaler", StandardScaler()),
                    ("model", LogisticRegression(random_state=42)),
                ]
            ),
            "neural_net": Pipeline(
                [
                    ("scaler", StandardScaler()),
                    (
                        "model",
                        MLPClassifier(
                            hidden_layer_sizes=(50, 25), max_iter=1000, random_state=42
                        ),
                    ),
                ]
            ),
        }

        # Train each model
        for name, model in models.items():
            try:
                model.fit(X_train, y_train)

                # Evaluate model
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, zero_division=0)
                recall = recall_score(y_test, y_pred, zero_division=0)
                f1 = f1_score(y_test, y_pred, zero_division=0)

                # Store performance metrics
                self.model_performance[f"{pair}_{name}"] = {
                    "accuracy": accuracy,
                    "precision": precision,
                    "recall": recall,
                    "f1": f1,
                }
            except Exception as e:
                logger.error(f"Error training model {name} for {pair}: {e}")
                continue

        # Update last training time
        self.last_training_time[pair] = current_time
        return True

    def predict(
        self, dataframe: pd.DataFrame, pair: str
    ) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """
        Make predictions using trained models
        """
        if not ML_DEPENDENCIES_AVAILABLE:
            return np.array([]), np.array([]), {}

        # Prepare features
        features = self.prepare_features(dataframe)

        if len(features) == 0:
            return np.array([]), np.array([]), {}

        # Make predictions with each model
        predictions = {}
        probabilities = {}

        for name, model in self.models.items():
            try:
                pred = model.predict(features)
                prob = model.predict_proba(features)[:, 1]  # Probability of class 1

                predictions[name] = pred
                probabilities[name] = prob
            except Exception as e:
                logger.error(f"Error predicting with model {name} for {pair}: {e}")

        # If no predictions were made, return empty arrays
        if not predictions:
            return np.array([]), np.array([]), {}

        # Combine predictions (simple majority vote)
        combined_pred = np.zeros(len(features))
        combined_prob = np.zeros(len(features))

        for name in predictions:
            combined_pred += predictions[name]
            combined_prob += probabilities[name]

        # Average the predictions and probabilities
        combined_pred = (combined_pred / len(predictions) > 0.5).astype(int)
        combined_prob = combined_prob / len(probabilities)

        # Calculate confidence intervals (simplified)
        confidence = {"lower": combined_prob - 0.1, "upper": combined_prob + 0.1}

        return combined_pred, combined_prob, confidence

    def get_feature_importance(self, pair: str) -> Dict[str, float]:
        """
        Get feature importance from trained models
        """
        if not ML_DEPENDENCIES_AVAILABLE:
            return {}

        # Return cached feature importance if available
        if pair in self.feature_importance:
            return self.feature_importance[pair]

        # Otherwise return empty dict
        return {}


class ThePalmTreeMasterStrategy(IStrategy):
    """
    ThePalmTree Master Strategy

    This is a comprehensive trading strategy that combines multiple technical indicators,
    machine learning predictions, market regime detection, and volume profile analysis
    to make trading decisions.

    Key features:
    - Adaptive to different market conditions
    - Session-aware trading (NY, London, Asia)
    - Machine learning integration
    - Q-learning for self-improvement
    - Volume profile analysis
    """

    # Strategy parameters
    minimal_roi = {
        "0": 0.05,
        "60": 0.025,
        "120": 0.015,
        "240": 0.01,
    }

    stoploss = -0.05
    timeframe = "5m"

    # Buy hyperspace params
    buy_params = {
        "adx_threshold": 25,
        "ema_long": 50,
        "ema_short": 12,
        "ml_threshold": 65,
        "rsi_buy": 30,
        "volume_threshold": 1.5,
    }

    # Sell hyperspace params
    sell_params = {
        "rsi_sell": 70,
    }

    # Parameters
    rsi_buy = IntParameter(20, 40, default=30, space="buy")
    rsi_sell = IntParameter(60, 80, default=70, space="sell")
    ema_short = IntParameter(5, 20, default=12, space="buy")
    ema_long = IntParameter(30, 100, default=50, space="buy")
    adx_threshold = IntParameter(15, 35, default=25, space="buy")
    volume_threshold = DecimalParameter(1.0, 3.0, default=1.5, space="buy")
    ml_threshold = IntParameter(40, 80, default=65, space="buy")
    use_ensemble_ml = CategoricalParameter([True, False], default=True, space="buy")
    retrain_period_days = IntParameter(1, 7, default=1, space="buy")
    use_multi_timeframe = CategoricalParameter([True, False], default=True, space="buy")
    bb_period = IntParameter(10, 30, default=20, space="buy")
    bb_std = DecimalParameter(1.5, 3.0, default=2.0, space="buy")
    keep_runner = CategoricalParameter([True, False], default=False, space="sell")
    runner_size = DecimalParameter(0.1, 0.5, default=0.3, space="sell")

    # DCA parameters
    use_dca = CategoricalParameter([True, False], default=False, space="buy")
    dca_threshold = DecimalParameter(-0.05, -0.01, default=-0.02, space="buy")

    # Q-learning parameters
    q_table = {}
    learning_rate = 0.1
    exploration_rate = 0.1

    def __init__(self, config: dict) -> None:
        """
        Initialize the strategy
        """
        super().__init__(config)

        # Initialize ML model
        self.ml_model = MLEnsembleModel(
            model_dir=os.path.join(config["user_data_dir"], "ml_models")
        )
        self.last_model_train_time = {}

        # Initialize trade statistics
        self.trade_stats = {
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "breakeven_trades": 0,
            "total_profit": 0,
            "total_loss": 0,
            "win_rate": 0,
            "avg_profit": 0,
            "avg_loss": 0,
            "profit_factor": 0,
            "sharpe": 0,
            "profits": [],
            "trade_durations": [],
        }

        # Initialize optimization parameters
        self.last_optimization_time = {}

    def get_chicago_time(self) -> str:
        """
        Get current time in Chicago timezone (CT)
        """
        chicago_tz = pytz.timezone("America/Chicago")
        chicago_time = datetime.now(pytz.utc).astimezone(chicago_tz)
        return chicago_time.strftime("%Y-%m-%d %H:%M:%S CT")

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Generate indicators for a dataframe
        """
        # Basic indicators
        dataframe["rsi"] = ta.RSI(dataframe)
        dataframe["cci"] = ta.CCI(dataframe)
        dataframe["mfi"] = ta.MFI(dataframe)
        dataframe["adx"] = ta.ADX(dataframe)

        # MACD
        macd = ta.MACD(dataframe)
        dataframe["macd"] = macd["macd"]
        dataframe["macdsignal"] = macd["macdsignal"]
        dataframe["macdhist"] = macd["macdhist"]

        # Bollinger Bands
        bollinger = ta.BBANDS(
            dataframe,
            timeperiod=self.bb_period.value,
            nbdevup=self.bb_std.value,
            nbdevdn=self.bb_std.value,
        )
        dataframe["bb_lowerband"] = bollinger["lowerband"]
        dataframe["bb_middleband"] = bollinger["middleband"]
        dataframe["bb_upperband"] = bollinger["upperband"]

        # EMA
        dataframe["ema_short"] = ta.EMA(dataframe, timeperiod=self.ema_short.value)
        dataframe["ema_long"] = ta.EMA(dataframe, timeperiod=self.ema_long.value)

        # Volume indicators
        dataframe["volume_mean_12"] = dataframe["volume"].rolling(12).mean()
        dataframe["volume_mean_24"] = dataframe["volume"].rolling(24).mean()
        dataframe["volume_norm"] = (
            dataframe["volume"] / dataframe["volume"].rolling(24).mean()
        )

        # ATR
        dataframe["atr"] = ta.ATR(dataframe)

        # Enhanced ML integration
        if self.use_ensemble_ml.value and ML_DEPENDENCIES_AVAILABLE:
            dataframe = self.train_and_predict_ml(dataframe, metadata)

        return dataframe

    def train_and_predict_ml(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Train ML models and make predictions
        """
        if not ML_DEPENDENCIES_AVAILABLE or not self.use_ensemble_ml.value:
            return dataframe

        pair = metadata["pair"]

        # Check if we need to retrain
        current_time = datetime.now()
        force_retrain = False

        if pair in self.last_model_train_time:
            last_train = self.last_model_train_time[pair]
            days_since_last_train = (current_time - last_train).total_seconds() / 86400

            if days_since_last_train >= self.retrain_period_days.value:
                force_retrain = True
                logger.info(
                    f"Retraining ML model for {pair} after {days_since_last_train:.1f} days"
                )
        else:
            force_retrain = True
            logger.info(f"Training ML model for {pair} for the first time")

        # Train models if needed
        if force_retrain:
            self.ml_model.train_models(dataframe, pair, force_retrain=force_retrain)
            self.last_model_train_time[pair] = current_time

        # Make predictions
        _, ml_probs, confidence = self.ml_model.predict(dataframe, pair)

        # Add predictions to dataframe
        dataframe["ml_up_prob"] = ml_probs * 100  # Convert to percentage

        # Add confidence intervals
        if "lower" in confidence and "upper" in confidence:
            dataframe["ml_conf_lower"] = confidence["lower"] * 100
            dataframe["ml_conf_upper"] = confidence["upper"] * 100
            dataframe["ml_conf_range"] = (
                confidence["upper"] - confidence["lower"]
            ) * 100

        # Add feature importance
        feature_importance = self.ml_model.get_feature_importance(pair)
        if feature_importance:
            # Log top 5 features
            top_features = sorted(
                feature_importance.items(), key=lambda x: x[1], reverse=True
            )[:5]
            feature_str = ", ".join(
                [f"{name}: {importance:.4f}" for name, importance in top_features]
            )
            logger.debug(f"Top 5 features for {pair}: {feature_str}")

        return dataframe

    def detect_market_regime(self, dataframe: DataFrame) -> np.ndarray:
        """
        Detect market regime (trending, ranging, volatile)
        """
        # Calculate volatility
        volatility = dataframe["atr"] / dataframe["close"]

        # Calculate trend strength
        trend_strength = dataframe["adx"]

        # Determine market regime
        regime = np.where(
            trend_strength > 25,
            "trending",
            np.where(
                volatility > volatility.rolling(20).mean() * 1.5, "volatile", "ranging"
            ),
        )

        return regime

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Generate entry signals
        """
        # Initialize entry conditions
        conditions = []

        # Price and EMA conditions
        price_ema_trend = (dataframe["close"] > dataframe["ema_short"]) & (
            dataframe["ema_short"] > dataframe["ema_long"]
        )
        conditions.append(price_ema_trend)

        # RSI condition
        rsi_condition = dataframe["rsi"] < self.rsi_buy.value
        conditions.append(rsi_condition)

        # ADX condition (trend strength)
        adx_condition = dataframe["adx"] > self.adx_threshold.value
        conditions.append(adx_condition)

        # Volume condition
        volume_condition = dataframe["volume_norm"] > self.volume_threshold.value
        conditions.append(volume_condition)

        # ML condition if available
        if "ml_up_prob" in dataframe.columns:
            ml_condition = dataframe["ml_up_prob"] > self.ml_threshold.value
            conditions.append(ml_condition)

        # Combine conditions - require at least 3 conditions to be met
        conditions_count = sum([condition.astype(int) for condition in conditions])
        dataframe.loc[conditions_count >= 3, "enter_long"] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Generate exit signals
        """
        # Exit signal conditions
        exit_conditions = (
            (dataframe["rsi"] > self.rsi_sell.value)  # RSI overbought
            | (dataframe["close"] > dataframe["bb_upperband"])  # Price above upper BB
        )

        # Apply ML filter if available
        if "ml_up_prob" in dataframe.columns:
            exit_conditions |= dataframe["ml_up_prob"] < (100 - self.ml_threshold.value)

        # Set exit signals
        dataframe.loc[exit_conditions, "exit_long"] = 1

        return dataframe

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time: datetime,
        entry_tag: Optional[str],
        side: str,
        **kwargs,
    ) -> bool:
        """
        Called before placing a buy order.
        :return: True if the trade should be executed, False otherwise
        """
        # Get current Chicago time
        chicago_time = self.get_chicago_time()

        # Log entry confirmation
        logger.info(
            f"Confirming {side} entry for {pair} at {rate:.8f} - Chicago Time: {chicago_time}"
        )

        # Apply Q-learning for entry decision
        state = f"{pair}_{entry_tag}_{side}"

        # Get Q-value for this state-action pair
        q_value = self.q_table.get(state, 0.5)  # Default to neutral if not seen before

        # Make decision based on Q-value and exploration
        if random.random() < self.exploration_rate:
            # Explore: random decision
            decision = random.random() > 0.3  # 70% chance to confirm during exploration
        else:
            # Exploit: use Q-value
            decision = q_value > 0.5

        # Log decision
        logger.info(
            f"Q-learning decision for {pair}: Q-value={q_value:.2f}, Decision={'Confirmed' if decision else 'Rejected'}"
        )

        return decision

    def confirm_trade_exit(
        self,
        pair: str,
        trade: "Trade",
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        exit_reason: str,
        current_time: datetime,
        **kwargs,
    ) -> bool:
        """
        Called before placing a sell order.
        :return: True if the trade should be executed, False otherwise
        """
        # Get current Chicago time
        chicago_time = self.get_chicago_time()

        # Log exit confirmation
        logger.info(
            f"Confirming exit for {pair} at {rate:.8f} - Chicago Time: {chicago_time} - Reason: {exit_reason}"
        )

        # Calculate profit
        current_profit = trade.calc_profit_ratio(rate)

        # Check if we should keep a runner
        if self.keep_runner.value and current_profit > 0.05:
            # Reduce position size to runner size
            trade.stake_amount *= self.runner_size.value
            return False

        # Update trade statistics
        self.update_trade_statistics(
            trade, order_type, amount, rate, current_profit, 0.0
        )

        # Update Q-learning
        state = f"{pair}_{trade.entry_tag}_{trade.trade_direction}"
        reward = current_profit * 100  # Convert to percentage

        # Update Q-value
        old_q_value = self.q_table.get(state, 0.5)
        new_q_value = old_q_value + self.learning_rate * (reward - old_q_value)
        self.q_table[state] = new_q_value

        return True

    def update_trade_statistics(
        self,
        trade: "Trade",
        order_type: str,
        amount: float,
        rate: float,
        profit: float,
        fee_cost: float,
    ) -> None:
        """
        Update trade statistics for performance tracking
        """
        # Update trade counts
        self.trade_stats["total_trades"] += 1

        if profit > 0.001:  # 0.1% profit threshold for winning trade
            self.trade_stats["winning_trades"] += 1
            self.trade_stats["total_profit"] += profit
        elif profit < -0.001:  # -0.1% loss threshold for losing trade
            self.trade_stats["losing_trades"] += 1
            self.trade_stats["total_loss"] += abs(profit)
        else:
            self.trade_stats["breakeven_trades"] += 1

        # Store profit for Sharpe ratio calculation
        self.trade_stats["profits"].append(profit)

        # Calculate trade duration
        if hasattr(trade, "open_date_utc") and hasattr(trade, "close_date_utc"):
            duration = (
                trade.close_date_utc - trade.open_date_utc
            ).total_seconds() / 3600  # hours
            self.trade_stats["trade_durations"].append(duration)

        # Update derived statistics
        if self.trade_stats["total_trades"] > 0:
            self.trade_stats["win_rate"] = (
                self.trade_stats["winning_trades"] / self.trade_stats["total_trades"]
            ) * 100
            self.trade_stats["avg_profit"] = self.trade_stats["total_profit"] / max(
                1, self.trade_stats["winning_trades"]
            )
            self.trade_stats["avg_loss"] = self.trade_stats["total_loss"] / max(
                1, self.trade_stats["losing_trades"]
            )

            if self.trade_stats["total_loss"] > 0:
                self.trade_stats["profit_factor"] = self.trade_stats[
                    "total_profit"
                ] / max(0.01, self.trade_stats["total_loss"])

            # Calculate Sharpe ratio if we have at least 10 trades
            if len(self.trade_stats["profits"]) >= 10:
                returns = np.array(self.trade_stats["profits"])
                self.trade_stats["sharpe"] = (
                    returns.mean() / max(0.0001, returns.std()) * np.sqrt(365)
                )  # Annualized
