#!/bin/zsh
# ThePalmTree Trading System Aliases for zsh
# Source this file in your .zshrc with: source /home/<USER>/.devcontainer/palmtree_aliases.zsh

# Main commands
alias palmtree-start="zsh /home/<USER>/.devcontainer/start_palmtree_all.sh"
alias palmtree-status="zsh /workspaces/freqtrade/check_palmtree_flexible.sh"

# Individual component commands
alias palmtree-freqtrade="cd /workspaces/freqtrade && freqtrade trade --strategy ThePalmTreeMasterStrategy"
alias palmtree-simple-dashboard="cd /workspaces/freqtrade/user_data/thepalmtree_dashboard && python server.py"
alias palmtree-dashboard="cd /workspaces/freqtrade/user_data/thepalmtree_dashboard && python simple_dashboard.py"
alias palmtree-logs="cd /workspaces/freqtrade/user_data/master_logs && python log_manager.py /workspaces/freqtrade/user_data/logs/current_run 5m 24"
alias palmtree-analytics="cd /workspaces/freqtrade/user_data/analytics && python thepalmtree_master_analytics.py start"

# Utility commands
alias palmtree-cd="cd /workspaces/freqtrade"
alias palmtree-edit-config="nano /workspaces/freqtrade/config.json"
alias palmtree-view-logs="tail -f /tmp/freqtrade.log /tmp/dashboard.log /tmp/simple_dashboard.log /tmp/master_logs.log /tmp/analytics.log"

# Add this line to your ~/.zshrc to load these aliases:
# source /home/<USER>/.devcontainer/palmtree_aliases.zsh

# Use echo -e to properly display ANSI color codes
# Only using neon green (\033[1;32m) and white (\033[0m) colors
echo -e "\n\033[1;32m=== ThePalmTree Trading System Aliases Loaded ===\033[0m"
echo -e "\033[1;32mAvailable Commands:\033[0m"
echo -e "  \033[1;32mpalmtree-start\033[0m       - Start all components"
echo -e "  \033[1;32mpalmtree-status\033[0m      - Check system status"
echo -e "  \033[1;32mpalmtree-freqtrade\033[0m   - Run the main FreqTrade bot"
echo -e "  \033[1;32mpalmtree-dashboard\033[0m   - Run the main dashboard (port 9090)"
echo -e "  \033[1;32mpalmtree-simple-dashboard\033[0m - Run the simple dashboard (port 8061)"
echo -e "  \033[1;32mpalmtree-logs\033[0m        - Run the Master Log Tracking"
echo -e "  \033[1;32mpalmtree-analytics\033[0m   - Run the ThePalmTree Analytics"
echo -e "  \033[1;32mpalmtree-view-logs\033[0m   - View all log files"
echo -e "  \033[1;32mpalmtree-cd\033[0m          - Change to FreqTrade directory"
echo -e "  \033[1;32mpalmtree-edit-config\033[0m - Edit FreqTrade config file"
echo -e "\033[1;32m================================================\033[0m\n"
