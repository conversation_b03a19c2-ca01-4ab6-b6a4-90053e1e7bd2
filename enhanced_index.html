<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ThePalmTree Trader Dashboard</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Enhanced styles for stats dropdown and balance cards */
        .stats-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .balance-card {
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            color: #000;
            font-weight: bold;
        }

        .balance-card .stat-icon {
            color: #000;
        }

        .stats-period-indicator {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid #00ff88;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 0.8em;
            color: #00ff88;
            margin-left: 10px;
        }

        .live-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: pulse 2s infinite;
            margin-right: 5px;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .error-indicator {
            color: #ff4444;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo-container">
                <img src="/static/img/logo.svg" alt="ThePalmTree Trader" class="logo">
                <div class="logo-text">ThePalmTree</div>
            </div>

            <div class="nav-title">Main</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt icon"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="trades">
                        <i class="fas fa-exchange-alt icon"></i> Trades
                    </a>
                </li>
            </ul>

            <div class="nav-title">Analysis</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="performance">
                        <i class="fas fa-chart-bar icon"></i> Performance
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="balance">
                        <i class="fas fa-wallet icon"></i> Balance
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="pairs">
                        <i class="fas fa-coins icon"></i> Pairs
                    </a>
                </li>
            </ul>

            <div class="nav-title">Settings</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="config">
                        <i class="fas fa-cog icon"></i> Configuration
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="logs">
                        <i class="fas fa-file-alt icon"></i> Logs
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/logout" class="nav-link">
                        <i class="fas fa-sign-out-alt icon"></i> Logout
                    </a>
                </li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Page -->
            <div class="page active" id="dashboard-page">
                <h1>ThePalmTree Trader Dashboard</h1>

                <!-- Bot Status -->
                <div class="card bot-status-card">
                    <div class="card-header">
                        <h2 class="card-title">Bot Status</h2>
                        <div class="card-actions">
                            <button class="btn btn-icon refresh-status" title="Refresh Status">
                                <i class="fas fa-sync-alt refresh-icon"></i>
                            </button>
                        </div>
                    </div>
                    <div id="bot-status-content">
                        <div class="spinner"></div>
                    </div>
                    <div class="bot-controls">
                        <button class="btn btn-control btn-start" id="start-bot" title="Start Bot">
                            <i class="fas fa-play"></i>
                            <span>Start</span>
                        </button>
                        <button class="btn btn-control btn-stop" id="stop-bot" title="Stop Bot">
                            <i class="fas fa-stop"></i>
                            <span>Stop</span>
                        </button>
                        <button class="btn btn-control btn-restart" id="restart-bot" title="Restart Bot">
                            <i class="fas fa-redo-alt"></i>
                            <span>Restart</span>
                        </button>
                        <button class="btn btn-control btn-settings" id="bot-settings" title="Bot Settings">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </button>
                    </div>
                </div>

                <!-- Enhanced Stats Overview -->
                <div class="stats-section">
                    <div class="stats-header">
                        <h2 class="stats-title">
                            <span class="live-indicator"></span>Trading Performance
                            <span class="stats-period-indicator" id="stats-period-indicator">Monthly Stats</span>
                        </h2>
                        <div class="stats-controls">
                            <select id="stats-period-select" class="tradingview-select">
                                <option value="daily">Today's Stats</option>
                                <option value="yesterday">Yesterday's Stats</option>
                                <option value="monthly" selected>Monthly Stats</option>
                            </select>
                            <button class="btn btn-icon refresh-stats" title="Refresh Stats">
                                <i class="fas fa-sync-alt refresh-icon"></i>
                            </button>
                        </div>
                    </div>
                    <div class="stats-grid">
                        <!-- Balance Cards -->
                        <div class="card stat-card balance-card">
                            <div class="stat-icon"><i class="fas fa-coins"></i></div>
                            <div class="stat-value" id="dry-run-balance">$1,000.00</div>
                            <div class="stat-label">Dry Run Balance</div>
                        </div>
                        <div class="card stat-card balance-card">
                            <div class="stat-icon"><i class="fas fa-wallet"></i></div>
                            <div class="stat-value" id="live-balance">Loading...</div>
                            <div class="stat-label">Bybit Live Balance</div>
                        </div>

                        <!-- Trading Stats Cards -->
                        <div class="card stat-card">
                            <div class="stat-icon"><i class="fas fa-exchange-alt"></i></div>
                            <div class="stat-value" id="total-trades">0</div>
                            <div class="stat-label">Total Trades</div>
                        </div>
                        <div class="card stat-card">
                            <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
                            <div class="stat-value" id="total-profit">0.00%</div>
                            <div class="stat-label">Total Profit</div>
                        </div>
                        <div class="card stat-card">
                            <div class="stat-icon"><i class="fas fa-percentage"></i></div>
                            <div class="stat-value" id="win-rate">0.0%</div>
                            <div class="stat-label">Win Rate</div>
                        </div>
                        <div class="card stat-card">
                            <div class="stat-icon"><i class="fas fa-chart-bar"></i></div>
                            <div class="stat-value" id="profit-factor">0.00</div>
                            <div class="stat-label">Profit Factor</div>
                        </div>
                        <div class="card stat-card">
                            <div class="stat-icon"><i class="fas fa-arrow-down"></i></div>
                            <div class="stat-value" id="max-drawdown">0.00%</div>
                            <div class="stat-label">Max Drawdown</div>
                        </div>
                        <div class="card stat-card">
                            <div class="stat-icon"><i class="fas fa-chart-area"></i></div>
                            <div class="stat-value" id="sharpe-ratio">0.00</div>
                            <div class="stat-label">Sharpe Ratio</div>
                        </div>
                    </div>
                </div>

                <!-- Open Trades -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">Open Trades</h2>
                        <div class="card-actions">
                            <button class="btn btn-icon refresh-trades">
                                <i class="fas fa-sync-alt refresh-icon"></i>
                            </button>
                        </div>
                    </div>
                    <div id="open-trades-content">
                        <div class="spinner"></div>
                    </div>
                </div>

                <!-- TradingView Chart (Keep as-is) -->
                <div class="card tradingview-widget-card">
                    <div class="card-header">
                        <h2 class="card-title">Market Chart</h2>
                        <div class="card-actions">
                            <button class="btn btn-icon minimize-btn" data-target="tradingview-container">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button class="btn btn-icon maximize-btn" data-target="tradingview-container" style="display: none;">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="tradingview-container" id="tradingview-container">
                        <div class="tradingview-header">
                            <div class="tradingview-controls">
                                <select id="symbol-select" class="tradingview-select">
                                    <option value="BTCUSDT">BTC/USDT</option>
                                    <option value="ETHUSDT">ETH/USDT</option>
                                    <option value="SOLUSDT">SOL/USDT</option>
                                    <option value="XRPUSDT">XRP/USDT</option>
                                    <option value="ADAUSDT">ADA/USDT</option>
                                    <option value="DOGEUSDT">DOGE/USDT</option>
                                    <option value="MATICUSDT">MATIC/USDT</option>
                                    <option value="AVAXUSDT">AVAX/USDT</option>
                                    <option value="LINKUSDT">LINK/USDT</option>
                                    <option value="UNIUSDT">UNI/USDT</option>
                                    <option value="LTCUSDT">LTC/USDT</option>
                                    <option value="BCHUSDT">BCH/USDT</option>
                                    <option value="XLMUSDT">XLM/USDT</option>
                                    <option value="VETUSDT">VET/USDT</option>
                                    <option value="TRXUSDT">TRX/USDT</option>
                                    <option value="EOSUSDT">EOS/USDT</option>
                                    <option value="ATOMUSDT">ATOM/USDT</option>
                                    <option value="NEARUSDT">NEAR/USDT</option>
                                    <option value="APEUSDT">APE/USDT</option>
                                    <option value="FTMUSDT">FTM/USDT</option>
                                </select>
                                <select id="interval-select" class="tradingview-select">
                                    <option value="1">1m</option>
                                    <option value="5" selected>5m</option>
                                    <option value="15">15m</option>
                                    <option value="30">30m</option>
                                    <option value="60">1h</option>
                                    <option value="240">4h</option>
                                    <option value="1D">1D</option>
                                </select>
                            </div>
                        </div>
                        <div id="tradingview_widget"></div>
                    </div>
                </div>
            </div>

            <!-- Other pages (Trades, Performance, etc.) -->
            <div class="page" id="trades-page">
                <h1>Trades</h1>
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">Closed Trades</h2>
                        <div class="card-actions">
                            <button class="btn btn-icon refresh-closed-trades">
                                <i class="fas fa-sync-alt refresh-icon"></i>
                            </button>
                        </div>
                    </div>
                    <div id="closed-trades-content">
                        <div class="spinner"></div>
                    </div>
                </div>
            </div>

            <!-- Performance Page -->
            <div class="page" id="performance-page">
                <h1>Performance Analysis</h1>
                <p>Performance analysis features coming soon...</p>
            </div>

            <!-- Balance Page -->
            <div class="page" id="balance-page">
                <h1>Balance Overview</h1>
                <p>Detailed balance information coming soon...</p>
            </div>

            <!-- Pairs Page -->
            <div class="page" id="pairs-page">
                <h1>Trading Pairs</h1>
                <p>Trading pairs analysis coming soon...</p>
            </div>

            <!-- Config Page -->
            <div class="page" id="config-page">
                <h1>Configuration</h1>
                <p>Bot configuration settings coming soon...</p>
            </div>

            <!-- Logs Page -->
            <div class="page" id="logs-page">
                <h1>System Logs</h1>
                <p>System logs viewer coming soon...</p>
            </div>
        </div>
    </div>

    <script src="https://s3.tradingview.com/tv.js"></script>
    <script>
        // Enhanced JavaScript with API integration
        let tradingViewWidget = null;
        let currentStatsData = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Navigation functionality
            const navLinks = document.querySelectorAll('.nav-link');
            const pages = document.querySelectorAll('.page');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetPage = this.getAttribute('data-page');
                    if (!targetPage) return;

                    // Remove active class from all nav links and pages
                    navLinks.forEach(nav => nav.classList.remove('active'));
                    pages.forEach(page => page.classList.remove('active'));

                    // Add active class to clicked nav link and corresponding page
                    this.classList.add('active');
                    document.getElementById(`${targetPage}-page`).classList.add('active');
                });
            });

            // Stats period selector
            document.getElementById('stats-period-select').addEventListener('change', function() {
                const period = this.value;
                loadStatsData(period);
                updateStatsIndicator(period);
            });

            // Refresh buttons
            document.querySelector('.refresh-stats').addEventListener('click', function() {
                const period = document.getElementById('stats-period-select').value;
                loadStatsData(period);
            });

            document.querySelector('.refresh-status').addEventListener('click', loadBotStatus);
            document.querySelector('.refresh-trades').addEventListener('click', loadOpenTrades);

            // Initialize dashboard
            loadStatsData('monthly');
            loadBalanceData();
            loadBotStatus();
            loadOpenTrades();
            initTradingViewWidgets();

            // Set up auto-refresh
            setInterval(() => {
                const period = document.getElementById('stats-period-select').value;
                loadStatsData(period);
                loadBalanceData();
            }, 60000); // Refresh every minute
        });

        // API Functions
        async function loadStatsData(period) {
            try {
                const response = await fetch(`/api/stats/${period}`);
                const data = await response.json();

                if (data.error) {
                    showNotification(`Error loading ${period} stats: ${data.error}`, 'error');
                    return;
                }

                currentStatsData = data;
                updateStatsDisplay(data);
                showNotification(`${period.charAt(0).toUpperCase() + period.slice(1)} stats updated`, 'success');
            } catch (error) {
                console.error('Error loading stats:', error);
                showNotification('Failed to load stats data', 'error');
            }
        }

        async function loadBalanceData() {
            try {
                const response = await fetch('/api/balance');
                const data = await response.json();

                if (data.error) {
                    document.getElementById('live-balance').innerHTML = '<span class="error-indicator">Error</span>';
                    return;
                }

                document.getElementById('dry-run-balance').textContent = `$${data.dry_run_balance.toFixed(2)}`;
                document.getElementById('live-balance').textContent = data.live_balance === 'API_CONNECTED' ? 'Connected' : `$${data.live_balance}`;
            } catch (error) {
                console.error('Error loading balance:', error);
                document.getElementById('live-balance').innerHTML = '<span class="error-indicator">Error</span>';
            }
        }

        function updateStatsDisplay(data) {
            document.getElementById('total-trades').textContent = data.total_trades || 0;
            document.getElementById('total-profit').textContent = `${(data.total_profit || 0).toFixed(2)}%`;
            document.getElementById('win-rate').textContent = `${(data.win_rate || 0).toFixed(1)}%`;
            document.getElementById('profit-factor').textContent = (data.profit_factor || 0).toFixed(2);
            document.getElementById('max-drawdown').textContent = `${(data.max_drawdown || 0).toFixed(2)}%`;
            document.getElementById('sharpe-ratio').textContent = (data.sharpe_ratio || 0).toFixed(2);
        }

        function updateStatsIndicator(period) {
            const indicator = document.getElementById('stats-period-indicator');
            const periodNames = {
                'daily': "Today's Stats",
                'yesterday': "Yesterday's Stats",
                'monthly': "Monthly Stats"
            };
            indicator.textContent = periodNames[period] || 'Stats';
        }

        function loadBotStatus() {
            // Placeholder for bot status
            document.getElementById('bot-status-content').innerHTML = `
                <div class="bot-status-info">
                    <div class="status-item">
                        <span class="status-label">Status:</span>
                        <span class="status-value online">Online</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Strategy:</span>
                        <span class="status-value">ThePalmTreeMasterStrategy</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Uptime:</span>
                        <span class="status-value">2h 15m</span>
                    </div>
                </div>
            `;
        }

        function loadOpenTrades() {
            // Placeholder for open trades
            document.getElementById('open-trades-content').innerHTML = `
                <div class="trades-table">
                    <p>No open trades currently</p>
                </div>
            `;
        }

        function showNotification(message, type = 'info') {
            // Simple notification system
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#00ff88' : type === 'error' ? '#ff4444' : '#00aaff'};
                color: #000;
                padding: 10px 15px;
                border-radius: 4px;
                z-index: 1000;
                font-weight: bold;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // TradingView Widget (Keep existing functionality)
        function initTradingViewWidgets() {
            if (typeof TradingView !== 'undefined') {
                tradingViewWidget = new TradingView.widget({
                    "width": "100%",
                    "height": 400,
                    "symbol": "BYBIT:BTCUSDT",
                    "interval": "5",
                    "timezone": "America/Chicago",
                    "theme": "dark",
                    "style": "1",
                    "locale": "en",
                    "toolbar_bg": "#1a1a1a",
                    "enable_publishing": false,
                    "hide_top_toolbar": false,
                    "hide_legend": false,
                    "save_image": false,
                    "container_id": "tradingview_widget",
                    "studies": ["RSI@tv-basicstudies", "MACD@tv-basicstudies"],
                    "overrides": {
                        "paneProperties.background": "#1a1a1a",
                        "paneProperties.vertGridProperties.color": "#2a2a2a",
                        "paneProperties.horzGridProperties.color": "#2a2a2a",
                        "symbolWatermarkProperties.transparency": 90,
                        "scalesProperties.textColor": "#00ff88"
                    }
                });
            }
        }
    </script>
</body>
</html>
