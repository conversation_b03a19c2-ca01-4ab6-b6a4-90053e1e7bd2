#!/usr/bin/env python3
"""
ThePalmTree Recent Trades
Shows the most recent trades with enhanced metrics including unrealized PnL
"""

import os
import sys
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import pytz
import argparse
import numpy as np
import math
import ccxt

# ANSI color codes - bright neon green
NEON_GREEN = "\033[38;5;46m"
RESET = "\033[0m"
BOLD = "\033[1m"

def get_chicago_time():
    """Get current time in Chicago timezone"""
    chicago_tz = pytz.timezone("America/Chicago")
    return datetime.now(pytz.utc).astimezone(chicago_tz)

# Sharpe ratio calculation removed to fix alias issue

def get_current_price(pair, exchange_name='binance'):
    """Get current price for a trading pair"""
    try:
        # Initialize exchange
        exchange_class = getattr(ccxt, exchange_name)
        exchange = exchange_class({
            'enableRateLimit': True,
        })

        # Format pair for exchange (e.g., BTC/USDT)
        formatted_pair = pair.replace('_', '/')

        # Fetch ticker
        ticker = exchange.fetch_ticker(formatted_pair)
        return ticker['last']
    except Exception as e:
        print(f"Error fetching price for {pair}: {str(e)}")
        return None

def get_recent_trades(db_path, limit=50, timestamp=None):
    """
    Get recent trades, optionally after a specific timestamp
    """
    # Connect to database
    conn = sqlite3.connect(db_path)

    # Build query
    query = """
    SELECT id, pair, open_date, close_date, open_rate, close_rate, stake_amount,
           amount, fee_open, fee_close,
           CAST((julianday(close_date) - julianday(open_date)) * 86400 AS INTEGER) AS trade_duration,
           close_profit AS profit_ratio, close_profit_abs AS profit_abs,
           exit_reason, enter_tag, is_open
    FROM trades
    """

    # Add timestamp filter if provided
    if timestamp:
        try:
            # Parse timestamp
            dt = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S")
            # Convert to UTC
            chicago_tz = pytz.timezone("America/Chicago")
            dt_chicago = chicago_tz.localize(dt)
            dt_utc = dt_chicago.astimezone(pytz.UTC)
            timestamp_str = dt_utc.strftime("%Y-%m-%d %H:%M:%S")

            query += f" WHERE open_date >= '{timestamp_str}'"
        except ValueError:
            print(f"Invalid timestamp format: {timestamp}. Using limit only.")

    # Add order and limit
    query += f" ORDER BY open_date DESC LIMIT {limit}"

    # Execute query
    trades_df = pd.read_sql_query(query, conn)
    conn.close()

    return trades_df

def determine_session(timestamp):
    """Determine trading session based on Chicago time"""
    if not timestamp:
        return "Unknown"

    # Convert to Chicago time
    chicago_tz = pytz.timezone("America/Chicago")

    # Handle both string and datetime inputs
    if isinstance(timestamp, str):
        try:
            dt = pd.to_datetime(timestamp)
            if dt.tzinfo is None:
                dt = pytz.utc.localize(dt)
            chicago_time = dt.astimezone(chicago_tz)
        except:
            return "Unknown"
    else:
        chicago_time = timestamp.astimezone(chicago_tz)

    # Get hour in Chicago time
    hour = chicago_time.hour

    # Determine session
    if 3 <= hour < 11:
        return "London Session"
    elif 11 <= hour < 17:
        return "New York Session"
    else:
        return "Evening Session"

def format_profit(profit):
    """Format profit with color and sign"""
    if profit > 0:
        return f"+{profit:.2%}"
    else:
        return f"{profit:.2%}"

def format_duration(seconds):
    """Format duration in a readable format"""
    if pd.isna(seconds):
        return "Still Open"

    days = seconds // (24 * 3600)
    seconds %= (24 * 3600)
    hours = seconds // 3600
    seconds %= 3600
    minutes = seconds // 60
    seconds %= 60

    if days > 0:
        return f"{int(days)}d {int(hours)}h {int(minutes)}m"
    elif hours > 0:
        return f"{int(hours)}h {int(minutes)}m"
    else:
        return f"{int(minutes)}m {int(seconds)}s"

def calculate_unrealized_pnl(trade, current_price):
    """Calculate unrealized PnL for an open trade"""
    # Check if trade is open and we have a current price
    if 'is_open' not in trade or not trade['is_open'] or current_price is None:
        return None

    # Calculate unrealized profit ratio
    unrealized_profit_ratio = (current_price / trade['open_rate']) - 1

    # Account for fees (simplified)
    if not pd.isna(trade['fee_open']):
        unrealized_profit_ratio -= trade['fee_open']

    return unrealized_profit_ratio

def generate_recent_trades_report(trades_df):
    """Generate a report of recent trades with enhanced metrics"""
    # Add session information
    trades_df['session'] = trades_df['open_date'].apply(determine_session)

    # Check if 'is_open' column exists and is a boolean
    if 'is_open' in trades_df.columns:
        # If it's not already a boolean, convert it
        if trades_df['is_open'].dtype != bool:
            trades_df['is_open'] = trades_df['is_open'].astype(bool)
    else:
        # If 'is_open' column doesn't exist, create it based on close_date
        trades_df['is_open'] = trades_df['close_date'].isna()

    # Calculate unrealized PnL for open trades
    open_trades = trades_df[trades_df['is_open'] == True]
    for i, trade in open_trades.iterrows():
        current_price = get_current_price(trade['pair'])
        trades_df.at[i, 'unrealized_pnl'] = calculate_unrealized_pnl(trade, current_price)

    # Calculate basic statistics
    total_trades = len(trades_df)
    closed_trades = trades_df[~trades_df['is_open']].shape[0]
    open_trades = trades_df[trades_df['is_open']].shape[0]

    # Calculate win rate
    winning_trades = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] > 0)].shape[0]
    losing_trades = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] < 0)].shape[0]

    win_rate = winning_trades / closed_trades if closed_trades > 0 else 0

    # Calculate average profit
    avg_profit = trades_df[~trades_df['is_open']]['profit_ratio'].mean() if closed_trades > 0 else 0

    # Calculate advanced metrics
    winning_trades_df = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] > 0)]
    losing_trades_df = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] < 0)]

    avg_winning_trade = winning_trades_df['profit_ratio'].mean() if len(winning_trades_df) > 0 else 0
    avg_losing_trade = losing_trades_df['profit_ratio'].mean() if len(losing_trades_df) > 0 else 0

    # Overall gain and loss
    total_gain = winning_trades_df['profit_abs'].sum() if len(winning_trades_df) > 0 else 0
    total_loss = losing_trades_df['profit_abs'].sum() if len(losing_trades_df) > 0 else 0

    # Calculate overall gain/loss percentage
    total_stake = trades_df[~trades_df['is_open']]['stake_amount'].sum() if closed_trades > 0 else 0
    gain_percentage = (total_gain / total_stake) * 100 if total_stake > 0 else 0
    loss_percentage = (total_loss / total_stake) * 100 if total_stake > 0 else 0

    # Net profit percentage
    net_percentage = gain_percentage - loss_percentage

    # Risk-reward ratio
    risk_reward = abs(avg_winning_trade / avg_losing_trade) if avg_losing_trade != 0 else 0

    # Profit factor
    profit_factor = total_gain / abs(total_loss) if abs(total_loss) > 0 else 0

    # Maximum drawdown
    max_drawdown = trades_df[~trades_df['is_open']]['profit_ratio'].min() if closed_trades > 0 else 0

    # Sharpe ratio calculation removed to fix alias issue

    # Print report
    print(f"\n{NEON_GREEN}{BOLD}================================================================================")
    print(f"ThePalmTree Recent Trades Summary (Chicago Time)")
    print(f"================================================================================")

    print(f"\nOverall Statistics:")
    print(f"Total Trades: {total_trades}")
    print(f"Closed Trades: {closed_trades}")
    print(f"Open Trades: {open_trades}")
    print(f"Win Rate: {win_rate:.2%}")
    print(f"Average Profit: {format_profit(avg_profit)}")

    print(f"\nTotal Performance:")
    print(f"Total Gain: +{gain_percentage:.2f}%")
    print(f"Total Loss: -{abs(loss_percentage):.2f}%")
    print(f"Net Profit: {'+' if net_percentage >= 0 else ''}{net_percentage:.2f}%")

    print(f"\nAdvanced Metrics:")
    print(f"Average Winning Trade: {format_profit(avg_winning_trade)}")
    print(f"Average Losing Trade: {format_profit(avg_losing_trade)}")
    print(f"Risk-Reward Ratio: {risk_reward:.2f}")
    print(f"Profit Factor: {profit_factor:.2f}")
    print(f"Maximum Drawdown: {format_profit(max_drawdown)}")
    # Sharpe Ratio removed to fix alias issue

    print(f"\nSession Distribution:")
    session_counts = trades_df['session'].value_counts()
    for session, count in session_counts.items():
        print(f"{session}: {count} trades")

    print(f"\nExit Reason Distribution:")
    exit_counts = trades_df[~trades_df['is_open']]['exit_reason'].value_counts()
    for reason, count in exit_counts.items():
        print(f"{reason}: {count} trades")

    print(f"\nTrade Details:")
    print(f"ID   Pair         Session         Open Time    Close Time   Duration   Profit     Unrealized PnL  Exit Reason         ")
    print(f"----------------------------------------------------------------------------------------------------")

    # Print trade details
    for _, trade in trades_df.iterrows():
        trade_id = trade['id']
        pair = trade['pair']
        session = trade['session']

        # Format open time
        open_time = pd.to_datetime(trade['open_date'])
        if open_time.tzinfo is None:
            open_time = pytz.utc.localize(open_time)
        chicago_open = open_time.astimezone(pytz.timezone("America/Chicago"))
        open_time_str = chicago_open.strftime("%I:%M:%S %p")

        # Format close time
        is_open = trade.get('is_open', pd.isna(trade['close_date']))
        if is_open:
            close_time_str = ""
        else:
            close_time = pd.to_datetime(trade['close_date'])
            if close_time.tzinfo is None:
                close_time = pytz.utc.localize(close_time)
            chicago_close = close_time.astimezone(pytz.timezone("America/Chicago"))
            close_time_str = chicago_close.strftime("%I:%M:%S %p")

        # Format duration
        if is_open:
            duration_str = "Still Open"
        else:
            duration_str = format_duration(trade['trade_duration'])

        # Format profit
        if is_open:
            profit_str = "Open"
            # Format unrealized PnL
            if 'unrealized_pnl' in trade and trade['unrealized_pnl'] is not None:
                unrealized_pnl_str = format_profit(trade['unrealized_pnl'])
            else:
                unrealized_pnl_str = "N/A"
        else:
            profit_str = format_profit(trade['profit_ratio'])
            unrealized_pnl_str = ""

        # Format exit reason
        exit_reason = trade['exit_reason'] if not pd.isna(trade['exit_reason']) else "Open"

        print(f"{trade_id:<4} {pair:<12} {session:<15} {open_time_str:<12} {close_time_str:<12} {duration_str:<10} {profit_str:<10} {unrealized_pnl_str:<15} {exit_reason:<20}")

    print(f"\n{NEON_GREEN}{BOLD}================================================================================")
    print(f"End of Recent Trades Summary")
    print(f"================================================================================{RESET}")

def main():
    parser = argparse.ArgumentParser(description='Show recent trades with enhanced metrics')
    parser.add_argument('--limit', type=int, default=50, help='Number of recent trades to show')
    parser.add_argument('--timestamp', type=str, help='Show trades after this timestamp (format: YYYY-MM-DD HH:MM:SS in Chicago time)')
    parser.add_argument('--chicago', action='store_true', help='Interpret timestamp as Chicago time')
    parser.add_argument('--db', type=str, default='tradesv3.sqlite', help='Path to the database file')

    args = parser.parse_args()

    # Get recent trades
    trades_df = get_recent_trades(args.db, args.limit, args.timestamp)

    # Generate report
    generate_recent_trades_report(trades_df)

if __name__ == "__main__":
    main()
