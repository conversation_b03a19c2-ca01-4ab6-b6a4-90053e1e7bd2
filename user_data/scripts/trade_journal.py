#!/usr/bin/env python3
"""
ThePalmTree Trade Journal
Generates daily summary cards of trading activity with session information
"""

import os
import sys
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import pytz
import argparse
import numpy as np

# ANSI color codes - bright neon green
NEON_GREEN = "\033[38;5;46m"
RESET = "\033[0m"
BOLD = "\033[1m"

def get_chicago_time():
    """Get current time in Chicago timezone"""
    chicago_tz = pytz.timezone("America/Chicago")
    return datetime.now(pytz.utc).astimezone(chicago_tz)

def calculate_sharpe_ratio(returns, risk_free_rate=0.02/365):
    """Calculate Sharpe ratio from a list of returns"""
    if len(returns) < 2:
        return 0.0

    returns_array = np.array(returns)
    excess_returns = returns_array - risk_free_rate

    # Avoid division by zero
    std_dev = np.std(excess_returns, ddof=1)
    if std_dev == 0:
        return 0.0

    # Annualized Sharpe Ratio
    sharpe = (np.mean(excess_returns) / std_dev) * np.sqrt(365)
    return sharpe

def get_trades_for_date(db_path, date_str=None):
    """
    Get all trades for a specific date
    """
    # Get date in Chicago timezone
    chicago_tz = pytz.timezone("America/Chicago")

    if date_str:
        try:
            dt = datetime.strptime(date_str, "%Y-%m-%d")
            dt = chicago_tz.localize(dt)
        except ValueError:
            print(f"Invalid date format: {date_str}. Using today's date.")
            dt = get_chicago_time()
    else:
        dt = get_chicago_time()

    # Get start and end of day in Chicago time
    start_of_day = dt.replace(hour=0, minute=0, second=0, microsecond=0)
    end_of_day = dt.replace(hour=23, minute=59, second=59, microsecond=999999)

    # Convert to UTC for database query
    start_of_day_utc = start_of_day.astimezone(pytz.UTC)
    end_of_day_utc = end_of_day.astimezone(pytz.UTC)

    # Format dates for SQL query
    start_date_str = start_of_day_utc.strftime("%Y-%m-%d %H:%M:%S")
    end_date_str = end_of_day_utc.strftime("%Y-%m-%d %H:%M:%S")

    # Connect to database
    conn = sqlite3.connect(db_path)

    # Get trades for the day
    query = f"""
    SELECT id, pair, open_date, close_date, open_rate, close_rate, stake_amount,
           amount, fee_open, fee_close,
           CAST((julianday(close_date) - julianday(open_date)) * 86400 AS INTEGER) AS trade_duration,
           close_profit AS profit_ratio, close_profit_abs AS profit_abs,
           exit_reason, enter_tag, is_open
    FROM trades
    WHERE (open_date BETWEEN '{start_date_str}' AND '{end_date_str}')
       OR (close_date BETWEEN '{start_date_str}' AND '{end_date_str}')
       OR (open_date <= '{start_date_str}' AND (close_date >= '{end_date_str}' OR close_date IS NULL))
    ORDER BY open_date
    """

    trades_df = pd.read_sql_query(query, conn)
    conn.close()

    return trades_df, dt.strftime("%Y-%m-%d")

def determine_session(timestamp):
    """Determine trading session based on Chicago time"""
    if not timestamp:
        return "Unknown"

    # Convert to Chicago time
    chicago_tz = pytz.timezone("America/Chicago")

    # Handle both string and datetime inputs
    if isinstance(timestamp, str):
        try:
            dt = pd.to_datetime(timestamp)
            if dt.tzinfo is None:
                dt = pytz.utc.localize(dt)
            chicago_time = dt.astimezone(chicago_tz)
        except:
            return "Unknown"
    else:
        chicago_time = timestamp.astimezone(chicago_tz)

    # Get hour in Chicago time
    hour = chicago_time.hour

    # Determine session
    if 3 <= hour < 11:
        return "London Session"
    elif 11 <= hour < 17:
        return "New York Session"
    else:
        return "Evening Session"

def format_profit(profit):
    """Format profit with color and sign"""
    if profit > 0:
        return f"+{profit:.2%}"
    else:
        return f"{profit:.2%}"

def format_duration(seconds):
    """Format duration in a readable format"""
    if pd.isna(seconds):
        return "Still Open"

    days = seconds // (24 * 3600)
    seconds %= (24 * 3600)
    hours = seconds // 3600
    seconds %= 3600
    minutes = seconds // 60
    seconds %= 60

    if days > 0:
        return f"{int(days)}d {int(hours)}h {int(minutes)}m"
    elif hours > 0:
        return f"{int(hours)}h {int(minutes)}m"
    else:
        return f"{int(minutes)}m {int(seconds)}s"

def generate_trade_journal(trades_df, date_str):
    """Generate a comprehensive trading journal"""
    # Check if 'is_open' column exists and is a boolean
    if 'is_open' in trades_df.columns:
        # If it's not already a boolean, convert it
        if trades_df['is_open'].dtype != bool:
            trades_df['is_open'] = trades_df['is_open'].astype(bool)
    else:
        # If 'is_open' column doesn't exist, create it based on close_date
        trades_df['is_open'] = trades_df['close_date'].isna()

    # Calculate basic statistics
    total_trades = len(trades_df)
    closed_trades = trades_df[~trades_df['is_open']].shape[0]
    open_trades = trades_df[trades_df['is_open']].shape[0]

    # Calculate win rate
    winning_trades = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] > 0)].shape[0]
    losing_trades = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] < 0)].shape[0]

    win_rate = winning_trades / closed_trades if closed_trades > 0 else 0

    # Calculate average profit
    avg_profit = trades_df[~trades_df['is_open']]['profit_ratio'].mean() if closed_trades > 0 else 0

    # Calculate advanced metrics
    winning_trades_df = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] > 0)]
    losing_trades_df = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] < 0)]

    avg_winning_trade = winning_trades_df['profit_ratio'].mean() if len(winning_trades_df) > 0 else 0
    avg_losing_trade = losing_trades_df['profit_ratio'].mean() if len(losing_trades_df) > 0 else 0

    # Overall daily gain and loss
    total_daily_gain = winning_trades_df['profit_abs'].sum() if len(winning_trades_df) > 0 else 0
    total_daily_loss = losing_trades_df['profit_abs'].sum() if len(losing_trades_df) > 0 else 0

    # Calculate overall daily gain/loss percentage
    total_stake = trades_df[~trades_df['is_open']]['stake_amount'].sum() if closed_trades > 0 else 0
    daily_gain_percentage = (total_daily_gain / total_stake) * 100 if total_stake > 0 else 0
    daily_loss_percentage = (total_daily_loss / total_stake) * 100 if total_stake > 0 else 0

    # Net daily profit percentage
    net_daily_percentage = daily_gain_percentage - daily_loss_percentage

    # Risk-reward ratio
    risk_reward = abs(avg_winning_trade / avg_losing_trade) if avg_losing_trade != 0 else 0

    # Profit factor
    profit_factor = total_daily_gain / abs(total_daily_loss) if abs(total_daily_loss) > 0 else 0

    # Maximum drawdown
    max_drawdown = trades_df[~trades_df['is_open']]['profit_ratio'].min() if closed_trades > 0 else 0

    # Calculate Sharpe ratio
    if closed_trades > 0:
        returns = trades_df[~trades_df['is_open']]['profit_ratio'].values
        sharpe_ratio = calculate_sharpe_ratio(returns)
    else:
        sharpe_ratio = 0.0

    # Add session information
    trades_df['session'] = trades_df['open_date'].apply(determine_session)

    # Session distribution
    session_counts = trades_df['session'].value_counts()

    # Exit reason distribution
    exit_counts = trades_df[~trades_df['is_open']]['exit_reason'].value_counts()

    # Print report
    print(f"\n{NEON_GREEN}{BOLD}================================================================================")
    print(f"ThePalmTree Trading Summary - {date_str} (Chicago Time)")
    print(f"================================================================================")

    print(f"\nOverall Statistics:")
    print(f"Total Trades: {total_trades}")
    print(f"Closed Trades: {closed_trades}")
    print(f"Open Trades: {open_trades}")
    print(f"Win Rate: {win_rate:.2%}")
    print(f"Average Profit: {format_profit(avg_profit)}")

    print(f"\nDaily Performance:")
    print(f"Total Daily Gain: +{daily_gain_percentage:.2f}%")
    print(f"Total Daily Loss: -{abs(daily_loss_percentage):.2f}%")
    print(f"Net Daily Profit: {'+' if net_daily_percentage >= 0 else ''}{net_daily_percentage:.2f}%")

    print(f"\nAdvanced Metrics:")
    print(f"Average Winning Trade: {format_profit(avg_winning_trade)}")
    print(f"Average Losing Trade: {format_profit(avg_losing_trade)}")
    print(f"Risk-Reward Ratio: {risk_reward:.2f}")
    print(f"Profit Factor: {profit_factor:.2f}")
    print(f"Maximum Drawdown: {format_profit(max_drawdown)}")
    print(f"Sharpe Ratio: {sharpe_ratio:.2f}")

    print(f"\nSession Distribution:")
    for session, count in session_counts.items():
        print(f"{session}: {count} trades")

    print(f"\nExit Reason Distribution:")
    for reason, count in exit_counts.items():
        print(f"{reason}: {count} trades")

    print(f"\nTrade Details:")
    print(f"ID   Pair         Session         Open Time    Close Time   Duration   Profit     Exit Reason         ")
    print(f"------------------------------------------------------------------------------------------")

    # Print trade details
    for _, trade in trades_df.iterrows():
        trade_id = trade['id']
        pair = trade['pair']
        session = trade['session']

        # Format open time
        open_time = pd.to_datetime(trade['open_date'])
        if open_time.tzinfo is None:
            open_time = pytz.utc.localize(open_time)
        chicago_open = open_time.astimezone(pytz.timezone("America/Chicago"))
        open_time_str = chicago_open.strftime("%I:%M:%S %p")

        # Format close time
        is_open = trade.get('is_open', pd.isna(trade['close_date']))
        if is_open:
            close_time_str = ""
        else:
            close_time = pd.to_datetime(trade['close_date'])
            if close_time.tzinfo is None:
                close_time = pytz.utc.localize(close_time)
            chicago_close = close_time.astimezone(pytz.timezone("America/Chicago"))
            close_time_str = chicago_close.strftime("%I:%M:%S %p")

        # Format duration
        if is_open:
            duration_str = "Still Open"
        else:
            duration_str = format_duration(trade['trade_duration'])

        # Format profit
        if is_open:
            profit_str = "Open"
        else:
            profit_str = format_profit(trade['profit_ratio'])

        # Format exit reason
        exit_reason = trade['exit_reason'] if not pd.isna(trade['exit_reason']) else "Open"

        print(f"{trade_id:<4} {pair:<12} {session:<15} {open_time_str:<12} {close_time_str:<12} {duration_str:<10} {profit_str:<10} {exit_reason:<20}")

    print(f"\n{NEON_GREEN}{BOLD}================================================================================")
    print(f"End of Summary")
    print(f"================================================================================{RESET}")

def main():
    parser = argparse.ArgumentParser(description='Generate trading journal')
    parser.add_argument('--date', type=str, help='Date to generate journal for (YYYY-MM-DD format)')
    parser.add_argument('--db', type=str, default='tradesv3.sqlite', help='Path to the database file')

    args = parser.parse_args()

    # Get trades for the date
    trades_df, date_str = get_trades_for_date(args.db, args.date)

    # Generate journal
    generate_trade_journal(trades_df, date_str)

if __name__ == "__main__":
    main()
