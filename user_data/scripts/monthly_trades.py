#!/usr/bin/env python3
"""
ThePalmTree Monthly Trade Journal
Generates monthly summary of trading activity with enhanced metrics
"""

import os
import sys
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import pytz
import argparse
import numpy as np
import math

# ANSI color codes - bright neon green
NEON_GREEN = "\033[38;5;46m"
RESET = "\033[0m"
BOLD = "\033[1m"

def get_chicago_time():
    """Get current time in Chicago timezone"""
    chicago_tz = pytz.timezone("America/Chicago")
    return datetime.now(pytz.utc).astimezone(chicago_tz)

def get_first_day_of_month(date_str=None):
    """Get the first day of the month for a given date or current month"""
    chicago_tz = pytz.timezone("America/Chicago")

    if date_str:
        try:
            dt = datetime.strptime(date_str, "%Y-%m-%d")
            dt = chicago_tz.localize(dt)
        except ValueError:
            print(f"Invalid date format: {date_str}. Using current month.")
            dt = get_chicago_time()
    else:
        dt = get_chicago_time()

    # Get first day of the month
    first_day = dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    return first_day

def calculate_sharpe_ratio(returns, risk_free_rate=0.02/365):
    """Calculate Sharpe ratio from a list of returns"""
    if len(returns) < 2:
        return 0.0

    returns_array = np.array(returns)
    excess_returns = returns_array - risk_free_rate

    # Avoid division by zero
    std_dev = np.std(excess_returns, ddof=1)
    if std_dev == 0:
        return 0.0

    # Annualized Sharpe Ratio
    sharpe = (np.mean(excess_returns) / std_dev) * np.sqrt(365)
    return sharpe

def calculate_sortino_ratio(returns, risk_free_rate=0.02/365):
    """Calculate Sortino ratio from a list of returns"""
    if len(returns) < 2:
        return 0.0

    returns_array = np.array(returns)
    excess_returns = returns_array - risk_free_rate

    # Calculate downside deviation (only negative returns)
    negative_returns = excess_returns[excess_returns < 0]

    if len(negative_returns) == 0 or np.std(negative_returns, ddof=1) == 0:
        return 0.0  # No negative returns or std dev is zero

    downside_deviation = np.std(negative_returns, ddof=1)

    # Annualized Sortino Ratio
    sortino = (np.mean(excess_returns) / downside_deviation) * np.sqrt(365)
    return sortino

def calculate_max_drawdown(returns):
    """Calculate maximum drawdown from a list of returns"""
    if not returns:
        return 0.0

    # Convert returns to cumulative returns
    cumulative = np.cumprod(1 + np.array(returns))

    # Calculate running maximum
    running_max = np.maximum.accumulate(cumulative)

    # Calculate drawdown
    drawdown = (cumulative - running_max) / running_max

    # Return the maximum drawdown
    return abs(np.min(drawdown))

def get_monthly_trades(db_path, month_date=None):
    """
    Get all trades for a specific month
    """
    # Get first day of month
    first_day = get_first_day_of_month(month_date)

    # Get last day of month
    if first_day.month == 12:
        last_day = first_day.replace(year=first_day.year+1, month=1) - timedelta(days=1)
    else:
        last_day = first_day.replace(month=first_day.month+1) - timedelta(days=1)

    # Set time to end of day
    last_day = last_day.replace(hour=23, minute=59, second=59)

    # Format dates for SQL query
    first_day_str = first_day.strftime("%Y-%m-%d %H:%M:%S")
    last_day_str = last_day.strftime("%Y-%m-%d %H:%M:%S")

    # Connect to database
    conn = sqlite3.connect(db_path)

    # Get trades for the month
    query = f"""
    SELECT id, pair, open_date, close_date, open_rate, close_rate, stake_amount,
           amount, fee_open, fee_close,
           CAST((julianday(close_date) - julianday(open_date)) * 86400 AS INTEGER) AS trade_duration,
           close_profit AS profit_ratio, close_profit_abs AS profit_abs,
           exit_reason, enter_tag, is_open
    FROM trades
    WHERE open_date BETWEEN '{first_day_str}' AND '{last_day_str}'
    ORDER BY open_date
    """

    trades_df = pd.read_sql_query(query, conn)
    conn.close()

    return trades_df, first_day, last_day

def determine_session(timestamp):
    """Determine trading session based on Chicago time"""
    if not timestamp:
        return "Unknown"

    # Convert to Chicago time
    chicago_tz = pytz.timezone("America/Chicago")

    # Handle both string and datetime inputs
    if isinstance(timestamp, str):
        try:
            dt = pd.to_datetime(timestamp)
            if dt.tzinfo is None:
                dt = pytz.utc.localize(dt)
            chicago_time = dt.astimezone(chicago_tz)
        except:
            return "Unknown"
    else:
        chicago_time = timestamp.astimezone(chicago_tz)

    # Get hour in Chicago time
    hour = chicago_time.hour

    # Determine session
    if 3 <= hour < 11:
        return "London Session"
    elif 11 <= hour < 17:
        return "New York Session"
    else:
        return "Evening Session"

def format_profit(profit):
    """Format profit with color and sign"""
    if profit > 0:
        return f"+{profit:.2%}"
    else:
        return f"{profit:.2%}"

def format_duration(seconds):
    """Format duration in a readable format"""
    if pd.isna(seconds):
        return "Still Open"

    days = seconds // (24 * 3600)
    seconds %= (24 * 3600)
    hours = seconds // 3600
    seconds %= 3600
    minutes = seconds // 60
    seconds %= 60

    if days > 0:
        return f"{int(days)}d {int(hours)}h {int(minutes)}m"
    elif hours > 0:
        return f"{int(hours)}h {int(minutes)}m"
    else:
        return f"{int(minutes)}m {int(seconds)}s"

def generate_monthly_report(trades_df, first_day, last_day):
    """Generate a comprehensive monthly trading report"""
    month_name = first_day.strftime("%B %Y")

    # Check if 'is_open' column exists and is a boolean
    if 'is_open' in trades_df.columns:
        # If it's not already a boolean, convert it
        if trades_df['is_open'].dtype != bool:
            trades_df['is_open'] = trades_df['is_open'].astype(bool)
    else:
        # If 'is_open' column doesn't exist, create it based on close_date
        trades_df['is_open'] = trades_df['close_date'].isna()

    # Calculate basic statistics
    total_trades = len(trades_df)
    closed_trades = trades_df[~trades_df['is_open']].shape[0]
    open_trades = trades_df[trades_df['is_open']].shape[0]

    # Calculate win rate
    winning_trades = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] > 0)].shape[0]
    losing_trades = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] < 0)].shape[0]

    win_rate = winning_trades / closed_trades if closed_trades > 0 else 0

    # Calculate average profit
    avg_profit = trades_df[~trades_df['is_open']]['profit_ratio'].mean() if closed_trades > 0 else 0

    # Calculate advanced metrics
    winning_trades_df = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] > 0)]
    losing_trades_df = trades_df[(~trades_df['is_open']) & (trades_df['profit_ratio'] < 0)]

    avg_winning_trade = winning_trades_df['profit_ratio'].mean() if len(winning_trades_df) > 0 else 0
    avg_losing_trade = losing_trades_df['profit_ratio'].mean() if len(losing_trades_df) > 0 else 0

    # Overall monthly gain and loss
    total_monthly_gain = winning_trades_df['profit_abs'].sum() if len(winning_trades_df) > 0 else 0
    total_monthly_loss = losing_trades_df['profit_abs'].sum() if len(losing_trades_df) > 0 else 0

    # Calculate overall monthly gain/loss percentage
    total_stake = trades_df[~trades_df['is_open']]['stake_amount'].sum() if closed_trades > 0 else 0
    monthly_gain_percentage = (total_monthly_gain / total_stake) * 100 if total_stake > 0 else 0
    monthly_loss_percentage = (total_monthly_loss / total_stake) * 100 if total_stake > 0 else 0

    # Net monthly profit percentage
    net_monthly_percentage = monthly_gain_percentage - monthly_loss_percentage

    # Risk-reward ratio
    risk_reward = abs(avg_winning_trade / avg_losing_trade) if avg_losing_trade != 0 else 0

    # Profit factor
    profit_factor = total_monthly_gain / abs(total_monthly_loss) if abs(total_monthly_loss) > 0 else 0

    # Maximum drawdown
    max_drawdown = trades_df[~trades_df['is_open']]['profit_ratio'].min() if closed_trades > 0 else 0

    # Calculate Sharpe and Sortino ratios
    daily_returns = []
    current_day = first_day
    while current_day <= last_day:
        next_day = current_day + timedelta(days=1)

        # Convert close_date to datetime and ensure timezone awareness
        close_dates = pd.to_datetime(trades_df['close_date'])
        close_dates_utc = close_dates.apply(
            lambda x: pytz.utc.localize(x) if x is not pd.NaT and x.tzinfo is None else x
        )

        # Filter trades for the current day
        day_trades = trades_df[(~trades_df['is_open']) &
                              (close_dates_utc >= current_day) &
                              (close_dates_utc < next_day)]

        if len(day_trades) > 0:
            daily_return = day_trades['profit_ratio'].mean()
            daily_returns.append(daily_return)

        current_day = next_day

    # Use simplified metrics instead of Sharpe/Sortino to avoid issues
    sharpe_ratio = 0.0
    sortino_ratio = 0.0
    if daily_returns:
        # Simple average daily return
        avg_return = sum(daily_returns) / len(daily_returns)
        # Simple volatility measure
        volatility = np.std(daily_returns) if len(daily_returns) > 1 else 1.0
        # Simple ratio
        sharpe_ratio = avg_return / volatility if volatility > 0 else 0.0

        # Simple downside deviation
        neg_returns = [r for r in daily_returns if r < 0]
        downside_dev = np.std(neg_returns) if len(neg_returns) > 1 else 1.0
        # Simple sortino
        sortino_ratio = avg_return / downside_dev if downside_dev > 0 else 0.0

    # Session distribution
    trades_df['session'] = trades_df['open_date'].apply(determine_session)
    session_counts = trades_df['session'].value_counts()

    # Exit reason distribution
    exit_counts = trades_df[~trades_df['is_open']]['exit_reason'].value_counts()

    # Print report
    print(f"\n{NEON_GREEN}{BOLD}================================================================================")
    print(f"ThePalmTree Monthly Trading Summary - {month_name} (Chicago Time)")
    print(f"================================================================================")

    print(f"\nOverall Statistics:")
    print(f"Total Trades: {total_trades}")
    print(f"Closed Trades: {closed_trades}")
    print(f"Open Trades: {open_trades}")
    print(f"Win Rate: {win_rate:.2%}")
    print(f"Average Profit: {format_profit(avg_profit)}")

    print(f"\nMonthly Performance:")
    print(f"Total Monthly Gain: +{monthly_gain_percentage:.2f}%")
    print(f"Total Monthly Loss: -{abs(monthly_loss_percentage):.2f}%")
    print(f"Net Monthly Profit: {'+' if net_monthly_percentage >= 0 else ''}{net_monthly_percentage:.2f}%")

    print(f"\nAdvanced Metrics:")
    print(f"Average Winning Trade: {format_profit(avg_winning_trade)}")
    print(f"Average Losing Trade: {format_profit(avg_losing_trade)}")
    print(f"Risk-Reward Ratio: {risk_reward:.2f}")
    print(f"Profit Factor: {profit_factor:.2f}")
    print(f"Maximum Drawdown: {format_profit(max_drawdown)}")
    print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
    print(f"Sortino Ratio: {sortino_ratio:.2f}")

    print(f"\nSession Distribution:")
    for session, count in session_counts.items():
        print(f"{session}: {count} trades")

    print(f"\nExit Reason Distribution:")
    for reason, count in exit_counts.items():
        print(f"{reason}: {count} trades")

    print(f"\nTrade Details:")
    print(f"ID   Pair         Session         Open Time    Close Time   Duration   Profit     Exit Reason         ")
    print(f"------------------------------------------------------------------------------------------")

    # Print trade details
    for _, trade in trades_df.iterrows():
        trade_id = trade['id']
        pair = trade['pair']
        session = trade['session']

        # Format open time
        open_time = pd.to_datetime(trade['open_date'])
        if open_time.tzinfo is None:
            open_time = pytz.utc.localize(open_time)
        chicago_open = open_time.astimezone(pytz.timezone("America/Chicago"))
        open_time_str = chicago_open.strftime("%I:%M:%S %p")

        # Format close time
        is_open = trade.get('is_open', pd.isna(trade['close_date']))
        if is_open:
            close_time_str = ""
        else:
            close_time = pd.to_datetime(trade['close_date'])
            if close_time.tzinfo is None:
                close_time = pytz.utc.localize(close_time)
            chicago_close = close_time.astimezone(pytz.timezone("America/Chicago"))
            close_time_str = chicago_close.strftime("%I:%M:%S %p")

        # Format duration
        if is_open:
            duration_str = "Still Open"
        else:
            duration_str = format_duration(trade['trade_duration'])

        # Format profit
        if is_open:
            profit_str = "Open"
        else:
            profit_str = format_profit(trade['profit_ratio'])

        # Format exit reason
        exit_reason = trade['exit_reason'] if not pd.isna(trade['exit_reason']) else "Open"

        print(f"{trade_id:<4} {pair:<12} {session:<15} {open_time_str:<12} {close_time_str:<12} {duration_str:<10} {profit_str:<10} {exit_reason:<20}")

    print(f"\n{NEON_GREEN}{BOLD}================================================================================")
    print(f"End of Monthly Summary")
    print(f"================================================================================{RESET}")

def main():
    parser = argparse.ArgumentParser(description='Generate monthly trading summary')
    parser.add_argument('--month', type=str, help='Month to generate report for (YYYY-MM-DD format, will use the month from this date)')
    parser.add_argument('--db', type=str, default='tradesv3.sqlite', help='Path to the database file')

    args = parser.parse_args()

    # Get trades for the month
    trades_df, first_day, last_day = get_monthly_trades(args.db, args.month)

    # Generate report
    generate_monthly_report(trades_df, first_day, last_day)

if __name__ == "__main__":
    main()
