#!/usr/bin/env python3
"""
ThePalmTree Trader Dashboard - Original Simple Dashboard
Neon green hacker aesthetic with embedded HTML, CSS, and JavaScript
Self-contained dashboard file
"""
import os
import time
import json
import base64
import logging
import http.server
import socketserver
from urllib.parse import parse_qs, urlparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ThePalmTreeDashboard')

# Dashboard configuration
PORT = 9090
USERNAME = "thepalmtree"
PASSWORD = "thepalmtree@55"
SESSION_TIMEOUT = 3600  # 1 hour

# Store active sessions
active_sessions = {}

class ThePalmTreeDashboardHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        logger.info(f"GET request for path: {self.path}")

        # Parse URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        # For the root path, check authentication and serve appropriate page
        if path == '/' or path == '/index.html':
            if self.is_authenticated():
                self.serve_dashboard()
            else:
                self.serve_login_page()
            return

        # Handle logout
        if path == '/logout':
            self.handle_logout()
            return

        # For other paths, serve 404 if not authenticated
        if not self.is_authenticated():
            self.serve_login_page()
        else:
            self.send_error(404)

    def do_POST(self):
        """Handle POST requests for login"""
        if self.path == '/login':
            self.handle_login()
        else:
            self.send_error(404)

    def handle_login(self):
        """Handle login authentication"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length).decode('utf-8')
            params = parse_qs(post_data)

            username = params.get('username', [''])[0]
            password = params.get('password', [''])[0]

            if username == USERNAME and password == PASSWORD:
                # Create session
                session_token = base64.b64encode(f"{username}:{time.time()}".encode()).decode()
                active_sessions[session_token] = {
                    'username': username,
                    'created': time.time(),
                    'expires': time.time() + SESSION_TIMEOUT
                }

                # Redirect to dashboard
                self.send_response(302)
                self.send_header('Location', '/')
                self.send_header('Set-Cookie', f'session={session_token}; Path=/; Max-Age={SESSION_TIMEOUT}')
                self.end_headers()
                logger.info(f"User {username} logged in successfully")
            else:
                # Invalid credentials
                self.serve_login_page(error=True)
                logger.warning(f"Failed login attempt for user: {username}")
        except Exception as e:
            logger.error(f"Login error: {e}")
            self.send_error(500)

    def handle_logout(self):
        """Handle logout"""
        # Get session token
        cookies = {}
        cookie_header = self.headers.get('Cookie', '')
        for cookie in cookie_header.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookies[name] = value

        session_token = cookies.get('session')
        if session_token and session_token in active_sessions:
            del active_sessions[session_token]

        # Redirect to login
        self.send_response(302)
        self.send_header('Location', '/')
        self.send_header('Set-Cookie', 'session=; Path=/; Max-Age=0')
        self.end_headers()
        logger.info("User logged out")

    def is_authenticated(self):
        """Check if user is authenticated"""
        # Get cookies
        cookies = {}
        cookie_header = self.headers.get('Cookie', '')
        for cookie in cookie_header.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookies[name] = value

        # Check session
        session_token = cookies.get('session')
        if not session_token or session_token not in active_sessions:
            return False

        # Check if session is expired
        session = active_sessions[session_token]
        if session['expires'] < time.time():
            del active_sessions[session_token]
            return False

        # Extend session
        session['expires'] = time.time() + SESSION_TIMEOUT
        return True

    def serve_login_page(self, error=False):
        """Serve the login page with original neon green hacker aesthetic"""
        error_message = ""
        if error:
            error_message = '<div class="error-message">⚠️ Invalid username or password</div>'

        login_html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌴 ThePalmTree Trader - Login</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            background: #0a0a0a;
            color: #00ff88;
            font-family: 'Courier New', monospace;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background-image:
                radial-gradient(circle at 25% 25%, #00ff8820 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #00ff8810 0%, transparent 50%);
        }}

        .login-container {{
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00ff88;
            border-radius: 15px;
            padding: 50px;
            box-shadow: 0 0 30px #00ff8850;
            text-align: center;
            min-width: 450px;
        }}

        .logo {{
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 0 0 30px #00ff88;
        }}

        .subtitle {{
            margin-bottom: 40px;
            opacity: 0.9;
            font-size: 1.2em;
            letter-spacing: 2px;
        }}

        .form-group {{
            margin-bottom: 25px;
            text-align: left;
        }}

        label {{
            display: block;
            margin-bottom: 8px;
            color: #00ff88;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}

        input[type="text"], input[type="password"] {{
            width: 100%;
            padding: 15px;
            background: #1a1a1a;
            border: 2px solid #00ff88;
            border-radius: 8px;
            color: #00ff88;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            transition: all 0.3s;
        }}

        input[type="text"]:focus, input[type="password"]:focus {{
            outline: none;
            box-shadow: 0 0 20px #00ff8850;
            border-color: #00cc6a;
            background: #222;
        }}

        .login-btn {{
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000;
            border: none;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 2px;
        }}

        .login-btn:hover {{
            background: linear-gradient(45deg, #00cc6a, #00ff88);
            box-shadow: 0 0 25px #00ff8850;
            transform: translateY(-2px);
        }}

        .error-message {{
            color: #ff4444;
            margin-bottom: 25px;
            padding: 15px;
            border: 2px solid #ff4444;
            border-radius: 8px;
            background: rgba(255, 68, 68, 0.1);
        }}
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🌴 ThePalmTree</div>
        <div class="subtitle">TRADER DASHBOARD</div>
        {error_message}
        <form method="post" action="/login">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="login-btn">ACCESS SYSTEM</button>
        </form>
    </div>
</body>
</html>"""

        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.end_headers()
        self.wfile.write(login_html.encode())

    def serve_dashboard(self):
        """Serve the main dashboard with original simple_dashboard neon green hacker aesthetic"""
        dashboard_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌴 ThePalmTree Trader Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #0a0a0a;
            color: #00ff88;
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            background-image:
                radial-gradient(circle at 25% 25%, #00ff8820 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #00ff8810 0%, transparent 50%);
        }

        .header {
            background: rgba(0, 0, 0, 0.95);
            border-bottom: 2px solid #00ff88;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 20px #00ff8850;
        }

        .logo {
            font-size: 2em;
            text-shadow: 0 0 20px #00ff88;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
        }

        .nav-item {
            color: #00ff88;
            text-decoration: none;
            padding: 10px 20px;
            border: 1px solid #00ff88;
            border-radius: 5px;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-item:hover {
            background: #00ff88;
            color: #000;
            box-shadow: 0 0 15px #00ff8850;
        }

        .container {
            padding: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ff88;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 0 20px #00ff8830;
            transition: all 0.3s;
        }

        .card:hover {
            box-shadow: 0 0 30px #00ff8850;
            transform: translateY(-5px);
        }

        .card-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 10px #00ff88;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 5px;
        }

        .stat-label {
            color: #00ff88;
            font-weight: bold;
        }

        .stat-value {
            color: #fff;
            font-weight: bold;
        }

        .chart-container {
            grid-column: 1 / -1;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ff88;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 0 20px #00ff8830;
        }

        .chart-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 10px #00ff88;
        }

        .tradingview-widget {
            width: 100%;
            height: 500px;
            border-radius: 10px;
            overflow: hidden;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .status-online {
            background: #00ff88;
            box-shadow: 0 0 10px #00ff88;
        }

        .status-offline {
            background: #ff4444;
            box-shadow: 0 0 10px #ff4444;
        }

        .refresh-btn {
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s;
            float: right;
        }

        .refresh-btn:hover {
            background: linear-gradient(45deg, #00cc6a, #00ff88);
            box-shadow: 0 0 15px #00ff8850;
        }

        .footer {
            text-align: center;
            padding: 20px;
            border-top: 1px solid #00ff88;
            margin-top: 50px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🌴 ThePalmTree Trader</div>
        <nav class="nav-menu">
            <a href="#" class="nav-item">Dashboard</a>
            <a href="#" class="nav-item">Trades</a>
            <a href="#" class="nav-item">Analytics</a>
            <a href="/logout" class="nav-item">Logout</a>
        </nav>
    </div>

    <div class="container">
        <div class="dashboard-grid">
            <div class="card">
                <h2 class="card-title">Bot Status</h2>
                <button class="refresh-btn" onclick="refreshStatus()">Refresh</button>
                <div class="stat-item">
                    <span class="stat-label">Status:</span>
                    <span class="stat-value">
                        <span class="status-indicator status-online"></span>Online
                    </span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Strategy:</span>
                    <span class="stat-value">ThePalmTreeMasterStrategy</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Uptime:</span>
                    <span class="stat-value" id="uptime">Loading...</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Open Trades:</span>
                    <span class="stat-value" id="open-trades">0</span>
                </div>
            </div>

            <div class="card">
                <h2 class="card-title">Account Balance</h2>
                <button class="refresh-btn" onclick="refreshBalance()">Refresh</button>
                <div class="stat-item">
                    <span class="stat-label">Dry Run Balance:</span>
                    <span class="stat-value" id="dry-balance">$1,000.00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Live Balance:</span>
                    <span class="stat-value" id="live-balance">Connected</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Total P&L:</span>
                    <span class="stat-value" id="total-pnl">+0.00%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Today's P&L:</span>
                    <span class="stat-value" id="today-pnl">+0.00%</span>
                </div>
            </div>

            <div class="card">
                <h2 class="card-title">Trading Stats</h2>
                <button class="refresh-btn" onclick="refreshStats()">Refresh</button>
                <div class="stat-item">
                    <span class="stat-label">Total Trades:</span>
                    <span class="stat-value" id="total-trades">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Win Rate:</span>
                    <span class="stat-value" id="win-rate">0.0%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Profit Factor:</span>
                    <span class="stat-value" id="profit-factor">0.00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Max Drawdown:</span>
                    <span class="stat-value" id="max-drawdown">0.00%</span>
                </div>
            </div>

            <div class="card">
                <h2 class="card-title">Recent Trades</h2>
                <button class="refresh-btn" onclick="refreshTrades()">Refresh</button>
                <div class="stat-item">
                    <span class="stat-label">Last Trade:</span>
                    <span class="stat-value" id="last-trade">None</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Last Profit:</span>
                    <span class="stat-value" id="last-profit">+0.00%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Avg Trade Time:</span>
                    <span class="stat-value" id="avg-trade-time">0m</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Best Trade:</span>
                    <span class="stat-value" id="best-trade">+0.00%</span>
                </div>
            </div>
        </div>

        <div class="chart-container">
            <h2 class="chart-title">Market Chart</h2>
            <div class="tradingview-widget" id="tradingview_widget"></div>
        </div>
    </div>

    <div class="footer">
        <p>🌴 ThePalmTree Trader Dashboard - Neon Green Hacker Aesthetic</p>
        <p>© 2025 ThePalmTree Trading System</p>
    </div>

    <script src="https://s3.tradingview.com/tv.js"></script>
    <script>
        // Initialize TradingView widget
        new TradingView.widget({
            "width": "100%",
            "height": 500,
            "symbol": "BYBIT:BTCUSDT",
            "interval": "5",
            "timezone": "America/Chicago",
            "theme": "dark",
            "style": "1",
            "locale": "en",
            "toolbar_bg": "#1a1a1a",
            "enable_publishing": false,
            "hide_top_toolbar": false,
            "hide_legend": false,
            "save_image": false,
            "container_id": "tradingview_widget",
            "studies": ["RSI@tv-basicstudies", "MACD@tv-basicstudies"],
            "overrides": {
                "paneProperties.background": "#1a1a1a",
                "paneProperties.vertGridProperties.color": "#2a2a2a",
                "paneProperties.horzGridProperties.color": "#2a2a2a",
                "symbolWatermarkProperties.transparency": 90,
                "scalesProperties.textColor": "#00ff88",
                "paneProperties.legendProperties.showLegend": true
            }
        });

        // Refresh functions
        function refreshStatus() {
            console.log('Refreshing bot status...');
            // Add actual refresh logic here
        }

        function refreshBalance() {
            console.log('Refreshing balance...');
            // Add actual refresh logic here
        }

        function refreshStats() {
            console.log('Refreshing trading stats...');
            // Add actual refresh logic here
        }

        function refreshTrades() {
            console.log('Refreshing recent trades...');
            // Add actual refresh logic here
        }

        // Auto-refresh every 30 seconds
        setInterval(function() {
            refreshStatus();
            refreshBalance();
            refreshStats();
            refreshTrades();
        }, 30000);

        // Update uptime
        function updateUptime() {
            const startTime = new Date().getTime() - (Math.random() * 3600000); // Random uptime
            const now = new Date().getTime();
            const uptime = now - startTime;
            const hours = Math.floor(uptime / 3600000);
            const minutes = Math.floor((uptime % 3600000) / 60000);
            document.getElementById('uptime').textContent = hours + 'h ' + minutes + 'm';
        }

        updateUptime();
        setInterval(updateUptime, 60000); // Update every minute
    </script>
</body>
</html>"""

        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.end_headers()
        self.wfile.write(dashboard_html.encode())

if __name__ == "__main__":
    logger.info(f"🌴 Starting ThePalmTree Trader Dashboard on port {PORT}")
    logger.info("🌴 Original Neon Green Hacker Aesthetic Dashboard")
    logger.info(f"🌴 Login credentials: {USERNAME} / {PASSWORD}")

    with socketserver.TCPServer(("", PORT), ThePalmTreeDashboardHandler) as httpd:
        logger.info(f"🌴 ThePalmTree Dashboard running at http://localhost:{PORT}")
        logger.info("🌴 Dashboard features:")
        logger.info("  - Neon green hacker aesthetic")
        logger.info("  - Login authentication")
        logger.info("  - TradingView chart integration")
        logger.info("  - Real-time trading stats")
        logger.info("  - Bot status monitoring")
        httpd.serve_forever()
