#!/usr/bin/env python3
"""
ThePalmTree Trader Dashboard - Original Simple Dashboard
Neon green hacker aesthetic with embedded HTML, CSS, and JavaScript
Self-contained dashboard file
"""
import os
import time
import json
import base64
import logging
import subprocess
import sqlite3
import http.server
import socketserver
from urllib.parse import parse_qs, urlparse
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ThePalmTreeDashboard')

# Dashboard configuration
PORT = 9090
USERNAME = "thepalmtree"
PASSWORD = "thepalmtree@55"
SESSION_TIMEOUT = 3600  # 1 hour

# Store active sessions
active_sessions = {}

class ThePalmTreeAPI:
    """API integration for ThePalmTree trading system"""

    def __init__(self):
        self.freqtrade_db = "/workspaces/freqtrade/tradesv3.dryrun.sqlite"

    def run_command(self, command):
        """Execute ThePalmTree alias commands"""
        try:
            cmd = f"source /home/<USER>/.devcontainer/thepalmtree_aliases.zsh && {command}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, executable='/bin/zsh')
            return result.stdout if result.returncode == 0 else None
        except Exception as e:
            logger.error(f"Error running command {command}: {e}")
            return None

    def get_bot_status(self):
        """Get FreqTrade bot status"""
        try:
            # Check if FreqTrade process is running
            result = subprocess.run(['pgrep', '-f', 'freqtrade'], capture_output=True, text=True)
            is_running = bool(result.stdout.strip())

            return {
                "status": "online" if is_running else "offline",
                "strategy": "ThePalmTreeMasterStrategy",
                "uptime": self._get_uptime() if is_running else "0h 0m",
                "open_trades": self._get_open_trades_count()
            }
        except Exception as e:
            logger.error(f"Error getting bot status: {e}")
            return {"status": "error", "strategy": "Unknown", "uptime": "0h 0m", "open_trades": 0}

    def get_balance_info(self):
        """Get account balance information"""
        try:
            balance_data = {
                "dry_run_balance": 1000.0,
                "live_balance": "Connected",
                "total_pnl": 0.0,
                "today_pnl": 0.0
            }

            # Try to get real balance from database
            if os.path.exists(self.freqtrade_db):
                conn = sqlite3.connect(self.freqtrade_db)
                cursor = conn.cursor()

                # Get total profit/loss
                cursor.execute("SELECT SUM(profit_abs) FROM trades WHERE is_open = 0")
                total_pnl = cursor.fetchone()[0] or 0.0
                balance_data["total_pnl"] = total_pnl

                # Get today's P&L
                today = datetime.now().strftime('%Y-%m-%d')
                cursor.execute("SELECT SUM(profit_abs) FROM trades WHERE is_open = 0 AND date(close_date) = ?", (today,))
                today_pnl = cursor.fetchone()[0] or 0.0
                balance_data["today_pnl"] = today_pnl

                conn.close()

            return balance_data
        except Exception as e:
            logger.error(f"Error getting balance info: {e}")
            return {"dry_run_balance": 1000.0, "live_balance": "Error", "total_pnl": 0.0, "today_pnl": 0.0}

    def get_trading_stats(self):
        """Get trading statistics"""
        try:
            output = self.run_command("thepalmtree-monthly-trades")
            if output:
                return self._parse_stats_output(output)
            return {"total_trades": 0, "win_rate": 0.0, "profit_factor": 0.0, "max_drawdown": 0.0}
        except Exception as e:
            logger.error(f"Error getting trading stats: {e}")
            return {"total_trades": 0, "win_rate": 0.0, "profit_factor": 0.0, "max_drawdown": 0.0}

    def get_recent_trades(self):
        """Get recent trades information"""
        try:
            if os.path.exists(self.freqtrade_db):
                conn = sqlite3.connect(self.freqtrade_db)
                cursor = conn.cursor()

                # Get last trade
                cursor.execute("SELECT pair, profit_ratio, close_date FROM trades WHERE is_open = 0 ORDER BY close_date DESC LIMIT 1")
                last_trade = cursor.fetchone()

                # Get best trade
                cursor.execute("SELECT profit_ratio FROM trades WHERE is_open = 0 ORDER BY profit_ratio DESC LIMIT 1")
                best_trade = cursor.fetchone()

                # Get average trade duration
                cursor.execute("SELECT AVG((julianday(close_date) - julianday(open_date)) * 24 * 60) FROM trades WHERE is_open = 0")
                avg_duration = cursor.fetchone()[0] or 0

                conn.close()

                return {
                    "last_trade": last_trade[0] if last_trade else "None",
                    "last_profit": f"{(last_trade[1] * 100):.2f}%" if last_trade else "+0.00%",
                    "avg_trade_time": f"{int(avg_duration)}m",
                    "best_trade": f"{(best_trade[0] * 100):.2f}%" if best_trade else "+0.00%"
                }

            return {"last_trade": "None", "last_profit": "+0.00%", "avg_trade_time": "0m", "best_trade": "+0.00%"}
        except Exception as e:
            logger.error(f"Error getting recent trades: {e}")
            return {"last_trade": "Error", "last_profit": "+0.00%", "avg_trade_time": "0m", "best_trade": "+0.00%"}

    def _get_uptime(self):
        """Get bot uptime"""
        try:
            result = subprocess.run(['ps', '-eo', 'pid,etime,cmd'], capture_output=True, text=True)
            for line in result.stdout.split('\n'):
                if 'freqtrade' in line and 'ThePalmTreeMasterStrategy' in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        return parts[1]
            return "0h 0m"
        except:
            return "0h 0m"

    def _get_open_trades_count(self):
        """Get count of open trades"""
        try:
            if os.path.exists(self.freqtrade_db):
                conn = sqlite3.connect(self.freqtrade_db)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM trades WHERE is_open = 1")
                count = cursor.fetchone()[0]
                conn.close()
                return count
            return 0
        except:
            return 0

    def _parse_stats_output(self, output):
        """Parse trading stats from command output"""
        stats = {"total_trades": 0, "win_rate": 0.0, "profit_factor": 0.0, "max_drawdown": 0.0}
        try:
            lines = output.split('\n')
            for line in lines:
                line = line.strip()
                if 'Total Trades:' in line:
                    stats["total_trades"] = int(line.split(':')[1].strip())
                elif 'Win Rate:' in line:
                    stats["win_rate"] = float(line.split(':')[1].strip().replace('%', ''))
                elif 'Profit Factor:' in line:
                    stats["profit_factor"] = float(line.split(':')[1].strip())
                elif 'Max Drawdown:' in line:
                    stats["max_drawdown"] = float(line.split(':')[1].strip().replace('%', ''))
        except:
            pass
        return stats

# Initialize API
palmtree_api = ThePalmTreeAPI()

class ThePalmTreeDashboardHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        logger.info(f"GET request for path: {self.path}")

        # Parse URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        # API endpoints
        if path.startswith('/api/'):
            if self.is_authenticated():
                self.handle_api_request(path)
            else:
                self.send_error(401)
            return

        # For the root path, check authentication and serve appropriate page
        if path == '/' or path == '/index.html':
            if self.is_authenticated():
                self.serve_dashboard()
            else:
                self.serve_login_page()
            return

        # Handle logout
        if path == '/logout':
            self.handle_logout()
            return

        # For other paths, serve 404 if not authenticated
        if not self.is_authenticated():
            self.serve_login_page()
        else:
            self.send_error(404)

    def handle_api_request(self, path):
        """Handle API requests for live data"""
        try:
            if path == '/api/bot/status':
                data = palmtree_api.get_bot_status()
            elif path == '/api/balance':
                data = palmtree_api.get_balance_info()
            elif path == '/api/stats':
                data = palmtree_api.get_trading_stats()
            elif path == '/api/trades/recent':
                data = palmtree_api.get_recent_trades()
            elif path == '/api/bot/start':
                data = self._handle_bot_control('start')
            elif path == '/api/bot/stop':
                data = self._handle_bot_control('stop')
            elif path == '/api/bot/restart':
                data = self._handle_bot_control('restart')
            else:
                data = {"error": "Unknown API endpoint"}

            # Send JSON response
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(data).encode())

        except Exception as e:
            logger.error(f"API error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())

    def _handle_bot_control(self, action):
        """Handle bot control actions"""
        try:
            if action == 'start':
                result = palmtree_api.run_command('thepalmtree-mainbot &')
                return {"success": True, "message": "Bot start command sent"}
            elif action == 'stop':
                subprocess.run(['pkill', '-f', 'freqtrade'], capture_output=True)
                return {"success": True, "message": "Bot stopped"}
            elif action == 'restart':
                subprocess.run(['pkill', '-f', 'freqtrade'], capture_output=True)
                time.sleep(2)
                palmtree_api.run_command('thepalmtree-mainbot &')
                return {"success": True, "message": "Bot restarted"}
            else:
                return {"success": False, "message": "Unknown action"}
        except Exception as e:
            return {"success": False, "message": str(e)}

    def do_POST(self):
        """Handle POST requests for login"""
        if self.path == '/login':
            self.handle_login()
        else:
            self.send_error(404)

    def handle_login(self):
        """Handle login authentication"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length).decode('utf-8')
            params = parse_qs(post_data)

            username = params.get('username', [''])[0]
            password = params.get('password', [''])[0]

            if username == USERNAME and password == PASSWORD:
                # Create session
                session_token = base64.b64encode(f"{username}:{time.time()}".encode()).decode()
                active_sessions[session_token] = {
                    'username': username,
                    'created': time.time(),
                    'expires': time.time() + SESSION_TIMEOUT
                }

                # Redirect to dashboard
                self.send_response(302)
                self.send_header('Location', '/')
                self.send_header('Set-Cookie', f'session={session_token}; Path=/; Max-Age={SESSION_TIMEOUT}')
                self.end_headers()
                logger.info(f"User {username} logged in successfully")
            else:
                # Invalid credentials
                self.serve_login_page(error=True)
                logger.warning(f"Failed login attempt for user: {username}")
        except Exception as e:
            logger.error(f"Login error: {e}")
            self.send_error(500)

    def handle_logout(self):
        """Handle logout"""
        # Get session token
        cookies = {}
        cookie_header = self.headers.get('Cookie', '')
        for cookie in cookie_header.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookies[name] = value

        session_token = cookies.get('session')
        if session_token and session_token in active_sessions:
            del active_sessions[session_token]

        # Redirect to login
        self.send_response(302)
        self.send_header('Location', '/')
        self.send_header('Set-Cookie', 'session=; Path=/; Max-Age=0')
        self.end_headers()
        logger.info("User logged out")

    def is_authenticated(self):
        """Check if user is authenticated"""
        # Get cookies
        cookies = {}
        cookie_header = self.headers.get('Cookie', '')
        for cookie in cookie_header.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookies[name] = value

        # Check session
        session_token = cookies.get('session')
        if not session_token or session_token not in active_sessions:
            return False

        # Check if session is expired
        session = active_sessions[session_token]
        if session['expires'] < time.time():
            del active_sessions[session_token]
            return False

        # Extend session
        session['expires'] = time.time() + SESSION_TIMEOUT
        return True

    def serve_login_page(self, error=False):
        """Serve the login page with original neon green hacker aesthetic"""
        error_message = ""
        if error:
            error_message = '<div class="error-message">⚠️ Invalid username or password</div>'

        login_html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌴 ThePalmTree Trader - Login</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            background: #0a0a0a;
            color: #00ff88;
            font-family: 'Courier New', monospace;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background-image:
                radial-gradient(circle at 25% 25%, #00ff8820 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #00ff8810 0%, transparent 50%);
        }}

        .login-container {{
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00ff88;
            border-radius: 15px;
            padding: 50px;
            box-shadow: 0 0 30px #00ff8850;
            text-align: center;
            min-width: 450px;
        }}

        .logo {{
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 0 0 30px #00ff88;
        }}

        .subtitle {{
            margin-bottom: 40px;
            opacity: 0.9;
            font-size: 1.2em;
            letter-spacing: 2px;
        }}

        .form-group {{
            margin-bottom: 25px;
            text-align: left;
        }}

        label {{
            display: block;
            margin-bottom: 8px;
            color: #00ff88;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}

        input[type="text"], input[type="password"] {{
            width: 100%;
            padding: 15px;
            background: #1a1a1a;
            border: 2px solid #00ff88;
            border-radius: 8px;
            color: #00ff88;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            transition: all 0.3s;
        }}

        input[type="text"]:focus, input[type="password"]:focus {{
            outline: none;
            box-shadow: 0 0 20px #00ff8850;
            border-color: #00cc6a;
            background: #222;
        }}

        .login-btn {{
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000;
            border: none;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 2px;
        }}

        .login-btn:hover {{
            background: linear-gradient(45deg, #00cc6a, #00ff88);
            box-shadow: 0 0 25px #00ff8850;
            transform: translateY(-2px);
        }}

        .error-message {{
            color: #ff4444;
            margin-bottom: 25px;
            padding: 15px;
            border: 2px solid #ff4444;
            border-radius: 8px;
            background: rgba(255, 68, 68, 0.1);
        }}
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🌴 ThePalmTree</div>
        <div class="subtitle">TRADER DASHBOARD</div>
        {error_message}
        <form method="post" action="/login">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="login-btn">ACCESS SYSTEM</button>
        </form>
    </div>
</body>
</html>"""

        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.end_headers()
        self.wfile.write(login_html.encode())

    def serve_dashboard(self):
        """Serve the main dashboard with original simple_dashboard neon green hacker aesthetic"""
        dashboard_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌴 ThePalmTree Trader Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #0a0a0a;
            color: #00ff88;
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            background-image:
                radial-gradient(circle at 25% 25%, #00ff8820 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #00ff8810 0%, transparent 50%);
        }

        .header {
            background: rgba(0, 0, 0, 0.95);
            border-bottom: 2px solid #00ff88;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 20px #00ff8850;
        }

        .logo {
            font-size: 2em;
            text-shadow: 0 0 20px #00ff88;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
        }

        .nav-item {
            color: #00ff88;
            text-decoration: none;
            padding: 10px 20px;
            border: 1px solid #00ff88;
            border-radius: 5px;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-item:hover {
            background: #00ff88;
            color: #000;
            box-shadow: 0 0 15px #00ff8850;
        }

        .nav-item.active {
            background: #00ff88;
            color: #000;
            box-shadow: 0 0 15px #00ff8850;
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        .container {
            padding: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ff88;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 0 20px #00ff8830;
            transition: all 0.3s;
        }

        .card:hover {
            box-shadow: 0 0 30px #00ff8850;
            transform: translateY(-5px);
        }

        .card-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 10px #00ff88;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 5px;
        }

        .stat-label {
            color: #00ff88;
            font-weight: bold;
        }

        .stat-value {
            color: #fff;
            font-weight: bold;
        }

        .chart-container {
            grid-column: 1 / -1;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ff88;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 0 20px #00ff8830;
        }

        .chart-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 10px #00ff88;
        }

        .tradingview-widget {
            width: 100%;
            height: 500px;
            border-radius: 10px;
            overflow: hidden;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .status-online {
            background: #00ff88;
            box-shadow: 0 0 10px #00ff88;
        }

        .status-offline {
            background: #ff4444;
            box-shadow: 0 0 10px #ff4444;
        }

        .refresh-btn {
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s;
            float: right;
        }

        .refresh-btn:hover {
            background: linear-gradient(45deg, #00cc6a, #00ff88);
            box-shadow: 0 0 15px #00ff8850;
        }

        .footer {
            text-align: center;
            padding: 20px;
            border-top: 1px solid #00ff88;
            margin-top: 50px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🌴 ThePalmTree Trader</div>
        <nav class="nav-menu">
            <a href="#" class="nav-item active" onclick="showPage('dashboard')">Dashboard</a>
            <a href="#" class="nav-item" onclick="showPage('trades')">Trades</a>
            <a href="#" class="nav-item" onclick="showPage('performance')">Performance</a>
            <a href="#" class="nav-item" onclick="showPage('balance')">Balance</a>
            <a href="#" class="nav-item" onclick="showPage('config')">Config</a>
            <a href="/logout" class="nav-item">Logout</a>
        </nav>
    </div>

    <div class="container">
        <!-- Dashboard Page -->
        <div id="dashboard-page" class="page active">
            <div class="dashboard-grid">
                <div class="card">
                    <h2 class="card-title">Bot Status</h2>
                    <button class="refresh-btn" onclick="refreshStatus()">Refresh</button>
                    <div class="stat-item">
                        <span class="stat-label">Status:</span>
                        <span class="stat-value">
                            <span class="status-indicator" id="bot-status-indicator"></span>
                            <span id="bot-status">Loading...</span>
                        </span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Strategy:</span>
                        <span class="stat-value" id="bot-strategy">Loading...</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Uptime:</span>
                        <span class="stat-value" id="uptime">Loading...</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Open Trades:</span>
                        <span class="stat-value" id="open-trades">Loading...</span>
                    </div>
                    <div style="margin-top: 20px; display: flex; gap: 10px;">
                        <button class="refresh-btn" onclick="controlBot('start')" style="background: #00ff88;">Start</button>
                        <button class="refresh-btn" onclick="controlBot('stop')" style="background: #ff4444;">Stop</button>
                        <button class="refresh-btn" onclick="controlBot('restart')" style="background: #ffaa00;">Restart</button>
                    </div>
                </div>

            <div class="card">
                <h2 class="card-title">Account Balance</h2>
                <button class="refresh-btn" onclick="refreshBalance()">Refresh</button>
                <div class="stat-item">
                    <span class="stat-label">Dry Run Balance:</span>
                    <span class="stat-value" id="dry-balance">$1,000.00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Live Balance:</span>
                    <span class="stat-value" id="live-balance">Connected</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Total P&L:</span>
                    <span class="stat-value" id="total-pnl">+0.00%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Today's P&L:</span>
                    <span class="stat-value" id="today-pnl">+0.00%</span>
                </div>
            </div>

            <div class="card">
                <h2 class="card-title">Trading Stats</h2>
                <button class="refresh-btn" onclick="refreshStats()">Refresh</button>
                <div class="stat-item">
                    <span class="stat-label">Total Trades:</span>
                    <span class="stat-value" id="total-trades">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Win Rate:</span>
                    <span class="stat-value" id="win-rate">0.0%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Profit Factor:</span>
                    <span class="stat-value" id="profit-factor">0.00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Max Drawdown:</span>
                    <span class="stat-value" id="max-drawdown">0.00%</span>
                </div>
            </div>

            <div class="card">
                <h2 class="card-title">Recent Trades</h2>
                <button class="refresh-btn" onclick="refreshTrades()">Refresh</button>
                <div class="stat-item">
                    <span class="stat-label">Last Trade:</span>
                    <span class="stat-value" id="last-trade">None</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Last Profit:</span>
                    <span class="stat-value" id="last-profit">+0.00%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Avg Trade Time:</span>
                    <span class="stat-value" id="avg-trade-time">0m</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Best Trade:</span>
                    <span class="stat-value" id="best-trade">+0.00%</span>
                </div>
            </div>
        </div>

            <div class="chart-container">
                <h2 class="chart-title">Market Chart</h2>
                <div class="tradingview-widget" id="tradingview_widget"></div>
            </div>
        </div>

        <!-- Trades Page -->
        <div id="trades-page" class="page">
            <h1 style="color: #00ff88; text-align: center; margin-bottom: 30px;">Recent Trades</h1>
            <div class="card">
                <h2 class="card-title">Trade History</h2>
                <button class="refresh-btn" onclick="refreshTrades()">Refresh</button>
                <div id="trades-table">
                    <p style="text-align: center; color: #00ff88;">Loading trades...</p>
                </div>
            </div>
        </div>

        <!-- Performance Page -->
        <div id="performance-page" class="page">
            <h1 style="color: #00ff88; text-align: center; margin-bottom: 30px;">Performance Analytics</h1>
            <div class="dashboard-grid">
                <div class="card">
                    <h2 class="card-title">Monthly Performance</h2>
                    <div id="monthly-stats">Loading...</div>
                </div>
                <div class="card">
                    <h2 class="card-title">Risk Metrics</h2>
                    <div id="risk-metrics">Loading...</div>
                </div>
            </div>
        </div>

        <!-- Balance Page -->
        <div id="balance-page" class="page">
            <h1 style="color: #00ff88; text-align: center; margin-bottom: 30px;">Account Balance</h1>
            <div class="dashboard-grid">
                <div class="card">
                    <h2 class="card-title">Live Balance</h2>
                    <div id="live-balance-details">Loading...</div>
                </div>
                <div class="card">
                    <h2 class="card-title">P&L Breakdown</h2>
                    <div id="pnl-breakdown">Loading...</div>
                </div>
            </div>
        </div>

        <!-- Config Page -->
        <div id="config-page" class="page">
            <h1 style="color: #00ff88; text-align: center; margin-bottom: 30px;">Bot Configuration</h1>
            <div class="card">
                <h2 class="card-title">Strategy Settings</h2>
                <p style="color: #00ff88;">Configuration management coming soon...</p>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>🌴 ThePalmTree Trader Dashboard - Neon Green Hacker Aesthetic</p>
        <p>© 2025 ThePalmTree Trading System</p>
    </div>

    <script src="https://s3.tradingview.com/tv.js"></script>
    <script>
        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // Remove active class from all nav items
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));

            // Show selected page
            document.getElementById(pageId + '-page').classList.add('active');

            // Add active class to clicked nav item
            event.target.classList.add('active');

            // Load page-specific data
            if (pageId === 'trades') {
                loadTradesData();
            } else if (pageId === 'performance') {
                loadPerformanceData();
            } else if (pageId === 'balance') {
                loadBalanceData();
            }
        }

        // Initialize TradingView widget
        new TradingView.widget({
            "width": "100%",
            "height": 500,
            "symbol": "BYBIT:BTCUSDT",
            "interval": "5",
            "timezone": "America/Chicago",
            "theme": "dark",
            "style": "1",
            "locale": "en",
            "toolbar_bg": "#1a1a1a",
            "enable_publishing": false,
            "hide_top_toolbar": false,
            "hide_legend": false,
            "save_image": false,
            "container_id": "tradingview_widget",
            "studies": ["RSI@tv-basicstudies", "MACD@tv-basicstudies"],
            "overrides": {
                "paneProperties.background": "#1a1a1a",
                "paneProperties.vertGridProperties.color": "#2a2a2a",
                "paneProperties.horzGridProperties.color": "#2a2a2a",
                "symbolWatermarkProperties.transparency": 90,
                "scalesProperties.textColor": "#00ff88",
                "paneProperties.legendProperties.showLegend": true
            }
        });

        // API functions
        async function fetchAPI(endpoint) {
            try {
                const response = await fetch(endpoint);
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                return { error: 'Failed to fetch data' };
            }
        }

        // Bot control functions
        async function controlBot(action) {
            try {
                const data = await fetchAPI(`/api/bot/${action}`);
                if (data.success) {
                    alert(`✅ ${data.message}`);
                    setTimeout(refreshStatus, 2000); // Refresh status after 2 seconds
                } else {
                    alert(`❌ ${data.message}`);
                }
            } catch (error) {
                alert(`❌ Error: ${error.message}`);
            }
        }

        // Refresh functions with real API calls
        async function refreshStatus() {
            try {
                const data = await fetchAPI('/api/bot/status');
                if (!data.error) {
                    document.getElementById('bot-status').textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
                    document.getElementById('bot-strategy').textContent = data.strategy;
                    document.getElementById('uptime').textContent = data.uptime;
                    document.getElementById('open-trades').textContent = data.open_trades;

                    const indicator = document.getElementById('bot-status-indicator');
                    indicator.className = 'status-indicator ' + (data.status === 'online' ? 'status-online' : 'status-offline');
                }
            } catch (error) {
                console.error('Error refreshing status:', error);
            }
        }

        async function refreshBalance() {
            try {
                const data = await fetchAPI('/api/balance');
                if (!data.error) {
                    document.getElementById('dry-balance').textContent = `$${data.dry_run_balance.toFixed(2)}`;
                    document.getElementById('live-balance').textContent = data.live_balance;
                    document.getElementById('total-pnl').textContent = `${data.total_pnl >= 0 ? '+' : ''}${data.total_pnl.toFixed(2)}%`;
                    document.getElementById('today-pnl').textContent = `${data.today_pnl >= 0 ? '+' : ''}${data.today_pnl.toFixed(2)}%`;
                }
            } catch (error) {
                console.error('Error refreshing balance:', error);
            }
        }

        async function refreshStats() {
            try {
                const data = await fetchAPI('/api/stats');
                if (!data.error) {
                    document.getElementById('total-trades').textContent = data.total_trades;
                    document.getElementById('win-rate').textContent = `${data.win_rate.toFixed(1)}%`;
                    document.getElementById('profit-factor').textContent = data.profit_factor.toFixed(2);
                    document.getElementById('max-drawdown').textContent = `${data.max_drawdown.toFixed(2)}%`;
                }
            } catch (error) {
                console.error('Error refreshing stats:', error);
            }
        }

        async function refreshTrades() {
            try {
                const data = await fetchAPI('/api/trades/recent');
                if (!data.error) {
                    document.getElementById('last-trade').textContent = data.last_trade;
                    document.getElementById('last-profit').textContent = data.last_profit;
                    document.getElementById('avg-trade-time').textContent = data.avg_trade_time;
                    document.getElementById('best-trade').textContent = data.best_trade;
                }
            } catch (error) {
                console.error('Error refreshing trades:', error);
            }
        }

        // Page-specific data loading
        function loadTradesData() {
            document.getElementById('trades-table').innerHTML = '<p style="text-align: center; color: #00ff88;">Loading recent trades...</p>';
            // Add trades table loading logic here
        }

        function loadPerformanceData() {
            document.getElementById('monthly-stats').innerHTML = 'Loading monthly performance...';
            document.getElementById('risk-metrics').innerHTML = 'Loading risk metrics...';
            // Add performance data loading logic here
        }

        function loadBalanceData() {
            document.getElementById('live-balance-details').innerHTML = 'Loading live balance details...';
            document.getElementById('pnl-breakdown').innerHTML = 'Loading P&L breakdown...';
            // Add balance data loading logic here
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            refreshBalance();
            refreshStats();
            refreshTrades();

            // Auto-refresh every 30 seconds
            setInterval(function() {
                refreshStatus();
                refreshBalance();
                refreshStats();
                refreshTrades();
            }, 30000);
        });
    </script>
</body>
</html>"""

        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.end_headers()
        self.wfile.write(dashboard_html.encode())

if __name__ == "__main__":
    logger.info(f"🌴 Starting ThePalmTree Trader Dashboard on port {PORT}")
    logger.info("🌴 Original Neon Green Hacker Aesthetic Dashboard")
    logger.info(f"🌴 Login credentials: {USERNAME} / {PASSWORD}")

    with socketserver.TCPServer(("", PORT), ThePalmTreeDashboardHandler) as httpd:
        logger.info(f"🌴 ThePalmTree Dashboard running at http://localhost:{PORT}")
        logger.info("🌴 Dashboard features:")
        logger.info("  - Neon green hacker aesthetic")
        logger.info("  - Login authentication")
        logger.info("  - TradingView chart integration")
        logger.info("  - Real-time trading stats")
        logger.info("  - Bot status monitoring")
        httpd.serve_forever()
