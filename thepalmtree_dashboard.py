#!/usr/bin/env python3
"""
ThePalmTree Trader Dashboard - Original Simple Dashboard
Neon green hacker aesthetic with embedded HTML, CSS, and JavaScript
Self-contained dashboard file
"""
import os
import time
import json
import base64
import logging
import http.server
import socketserver
from urllib.parse import parse_qs, urlparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ThePalmTreeDashboard')

# Dashboard configuration
PORT = 9090
USERNAME = "thepalmtree"
PASSWORD = "thepalmtree@55"
SESSION_TIMEOUT = 3600  # 1 hour

# Store active sessions
active_sessions = {}

class ThePalmTreeDashboardHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        logger.info(f"GET request for path: {self.path}")
        
        # Parse URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        
        # For the root path, check authentication and serve appropriate page
        if path == '/' or path == '/index.html':
            if self.is_authenticated():
                self.serve_dashboard()
            else:
                self.serve_login_page()
            return
        
        # Handle logout
        if path == '/logout':
            self.handle_logout()
            return
        
        # For other paths, serve 404 if not authenticated
        if not self.is_authenticated():
            self.serve_login_page()
        else:
            self.send_error(404)

    def do_POST(self):
        """Handle POST requests for login"""
        if self.path == '/login':
            self.handle_login()
        else:
            self.send_error(404)

    def handle_login(self):
        """Handle login authentication"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length).decode('utf-8')
            params = parse_qs(post_data)

            username = params.get('username', [''])[0]
            password = params.get('password', [''])[0]

            if username == USERNAME and password == PASSWORD:
                # Create session
                session_token = base64.b64encode(f"{username}:{time.time()}".encode()).decode()
                active_sessions[session_token] = {
                    'username': username,
                    'created': time.time(),
                    'expires': time.time() + SESSION_TIMEOUT
                }

                # Redirect to dashboard
                self.send_response(302)
                self.send_header('Location', '/')
                self.send_header('Set-Cookie', f'session={session_token}; Path=/; Max-Age={SESSION_TIMEOUT}')
                self.end_headers()
                logger.info(f"User {username} logged in successfully")
            else:
                # Invalid credentials
                self.serve_login_page(error=True)
                logger.warning(f"Failed login attempt for user: {username}")
        except Exception as e:
            logger.error(f"Login error: {e}")
            self.send_error(500)

    def handle_logout(self):
        """Handle logout"""
        # Get session token
        cookies = {}
        cookie_header = self.headers.get('Cookie', '')
        for cookie in cookie_header.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookies[name] = value

        session_token = cookies.get('session')
        if session_token and session_token in active_sessions:
            del active_sessions[session_token]

        # Redirect to login
        self.send_response(302)
        self.send_header('Location', '/')
        self.send_header('Set-Cookie', 'session=; Path=/; Max-Age=0')
        self.end_headers()
        logger.info("User logged out")

    def is_authenticated(self):
        """Check if user is authenticated"""
        # Get cookies
        cookies = {}
        cookie_header = self.headers.get('Cookie', '')
        for cookie in cookie_header.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookies[name] = value

        # Check session
        session_token = cookies.get('session')
        if not session_token or session_token not in active_sessions:
            return False

        # Check if session is expired
        session = active_sessions[session_token]
        if session['expires'] < time.time():
            del active_sessions[session_token]
            return False

        # Extend session
        session['expires'] = time.time() + SESSION_TIMEOUT
        return True

    def serve_login_page(self, error=False):
        """Serve the login page with original neon green hacker aesthetic"""
        error_message = ""
        if error:
            error_message = '<div class="error-message">⚠️ Invalid username or password</div>'
        
        login_html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌴 ThePalmTree Trader - Login</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            background: #0a0a0a;
            color: #00ff88;
            font-family: 'Courier New', monospace;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background-image: 
                radial-gradient(circle at 25% 25%, #00ff8820 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #00ff8810 0%, transparent 50%);
        }}
        
        .login-container {{
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00ff88;
            border-radius: 15px;
            padding: 50px;
            box-shadow: 0 0 30px #00ff8850;
            text-align: center;
            min-width: 450px;
        }}
        
        .logo {{
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 0 0 30px #00ff88;
        }}
        
        .subtitle {{
            margin-bottom: 40px;
            opacity: 0.9;
            font-size: 1.2em;
            letter-spacing: 2px;
        }}
        
        .form-group {{
            margin-bottom: 25px;
            text-align: left;
        }}
        
        label {{
            display: block;
            margin-bottom: 8px;
            color: #00ff88;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        
        input[type="text"], input[type="password"] {{
            width: 100%;
            padding: 15px;
            background: #1a1a1a;
            border: 2px solid #00ff88;
            border-radius: 8px;
            color: #00ff88;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            transition: all 0.3s;
        }}
        
        input[type="text"]:focus, input[type="password"]:focus {{
            outline: none;
            box-shadow: 0 0 20px #00ff8850;
            border-color: #00cc6a;
            background: #222;
        }}
        
        .login-btn {{
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000;
            border: none;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 2px;
        }}
        
        .login-btn:hover {{
            background: linear-gradient(45deg, #00cc6a, #00ff88);
            box-shadow: 0 0 25px #00ff8850;
            transform: translateY(-2px);
        }}
        
        .error-message {{
            color: #ff4444;
            margin-bottom: 25px;
            padding: 15px;
            border: 2px solid #ff4444;
            border-radius: 8px;
            background: rgba(255, 68, 68, 0.1);
        }}
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🌴 ThePalmTree</div>
        <div class="subtitle">TRADER DASHBOARD</div>
        {error_message}
        <form method="post" action="/login">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="login-btn">ACCESS SYSTEM</button>
        </form>
    </div>
</body>
</html>"""
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.end_headers()
        self.wfile.write(login_html.encode())
