#!/usr/bin/env python3
import sys
import os
import subprocess
import time
import datetime
import logging
import logging.config
import pytz

# Set timezone to Chicago time (US/Central)
os.environ['TZ'] = 'America/Chicago'
time.tzset()

# ANSI color codes for neon green text
GREEN = "\033[38;5;46m"
RESET = "\033[0m"

# Custom formatter that uses Chicago time and 12-hour format
class ChicagoTimeFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        # Convert to Chicago time
        chicago_tz = pytz.timezone('America/Chicago')
        created = datetime.datetime.fromtimestamp(record.created)
        chicago_time = chicago_tz.localize(created)
        
        # Format with 12-hour clock and AM/PM
        if datefmt:
            return chicago_time.strftime(datefmt)
        else:
            return chicago_time.strftime("%I:%M:%S %p")  # 12-hour format with AM/PM

def setup_logging():
    # Get the root logger
    root = logging.getLogger()
    
    # Remove existing handlers
    for handler in root.handlers[:]:
        root.removeHandler(handler)
    
    # Create console handler with custom formatter
    console = logging.StreamHandler(sys.stdout)
    console.setLevel(logging.INFO)
    
    # Create formatter with Chicago time
    formatter = ChicagoTimeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console.setFormatter(formatter)
    
    # Add handler to root logger
    root.addHandler(console)
    root.setLevel(logging.INFO)

def print_green(text):
    print(f"{GREEN}{text}{RESET}")

def main():
    # Set up custom logging
    setup_logging()
    
    # Get the FreqTrade command arguments
    freqtrade_args = sys.argv[1:]
    
    # Print startup message
    print_green("=" * 80)
    current_time = datetime.datetime.now().strftime('%I:%M:%S %p')
    print_green(f"Starting ThePalmTree Trading Bot - {current_time} Chicago Time")
    print_green("=" * 80)
    
    # Construct the FreqTrade command with environment variables for Chicago time
    env = os.environ.copy()
    env['TZ'] = 'America/Chicago'
    
    # Run FreqTrade with the arguments
    cmd = ["freqtrade"] + freqtrade_args
    
    try:
        # Use subprocess.call to inherit the current process environment
        # This allows FreqTrade to use our custom logging setup
        return subprocess.call(cmd, env=env)
    except KeyboardInterrupt:
        print_green("\nShutting down ThePalmTree Trading Bot...")
        return 0
    except Exception as e:
        print(f"Error running FreqTrade: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
