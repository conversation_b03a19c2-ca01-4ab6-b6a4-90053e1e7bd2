#!/usr/bin/env python3
import sys
import os
import subprocess
import time

# ANSI color codes for neon green text
GREEN = "\033[38;5;46m"
RESET = "\033[0m"

def print_green(text):
    print(f"{GREEN}{text}{RESET}")

def main():
    # Get the FreqTrade command arguments
    freqtrade_args = sys.argv[1:]
    
    # Print startup message
    print_green("=" * 80)
    print_green(f"Starting ThePalmTree Trading Bot - {time.strftime('%I:%M:%S %p')} Chicago Time")
    print_green("=" * 80)
    
    # Construct the FreqTrade command
    cmd = ["freqtrade"] + freqtrade_args
    
    # Run FreqTrade with the arguments
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                  universal_newlines=True, bufsize=1)
        
        # Process and colorize the output
        for line in process.stdout:
            if "ERROR" in line or "error" in line.lower():
                print(line.rstrip())
            elif "WARNING" in line or "warning" in line.lower():
                print(line.rstrip())
            else:
                print_green(line.rstrip())
                
        process.wait()
        return process.returncode
    except KeyboardInterrupt:
        print_green("\nShutting down ThePalmTree Trading Bot...")
        return 0
    except Exception as e:
        print(f"Error running FreqTrade: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
