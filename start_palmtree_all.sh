#!/bin/zsh
# Script to start all ThePalmTree Trading System components
#
# This script starts all the necessary components of the ThePalmTree Trading System:
# 1. FreqTrade with ThePalmTreeMasterStrategy
# 2. ThePalmTree Simple Dashboard (on port 8061)
# 3. ThePalmTree Dashboard (main server on port 9090)
# 4. Master Log Tracking
# 5. ThePalmTree Master Analytics
#
# Run this script in a zsh terminal with: zsh /home/<USER>/.devcontainer/start_palmtree_all.sh

echo "=== Starting ThePalmTree Trading System ==="

# Start FreqTrade with ThePalmTreeMasterStrategy
echo "Starting FreqTrade with ThePalmTreeMasterStrategy..."
cd /workspaces/freqtrade
# Run FreqTrade in a background process with Chicago timezone
nohup python3 /home/<USER>/.devcontainer/run_freqtrade_formatted.py trade --strategy ThePalmTreeMasterStrategy --logfile /tmp/freqtrade.log > /tmp/freqtrade.log 2>&1 &
FREQTRADE_PID=$!
echo "FreqTrade started with PID: $FREQTRADE_PID"

# Start ThePalmTree Simple Dashboard (on port 8061)
echo "Starting ThePalmTree Simple Dashboard..."
cd /workspaces/freqtrade/user_data/thepalmtree_dashboard
# Run the dashboard in a background process
nohup python server.py > /tmp/dashboard.log 2>&1 &
DASHBOARD_PID=$!
echo "ThePalmTree Simple Dashboard started with PID: $DASHBOARD_PID"

# Start ThePalmTree Dashboard (main server on port 9090)
echo "Starting ThePalmTree Dashboard (main server)..."
cd /workspaces/freqtrade/user_data/thepalmtree_dashboard
# Run the simple dashboard in a background process
nohup python simple_dashboard.py > /tmp/simple_dashboard.log 2>&1 &
SIMPLE_DASHBOARD_PID=$!
echo "ThePalmTree Dashboard started with PID: $SIMPLE_DASHBOARD_PID"

# Start Master Log Tracking
echo "Starting Master Log Tracking..."
cd /workspaces/freqtrade/user_data/master_logs

# Create a log directory if it doesn't exist
mkdir -p /workspaces/freqtrade/user_data/logs/current_run

# Run the log manager in a background process with the current run directory
nohup python log_manager.py /workspaces/freqtrade/user_data/logs/current_run 5m 24 > /tmp/master_logs.log 2>&1 &
LOGS_PID=$!
echo "Master Log Tracking started with PID: $LOGS_PID"

# Start ThePalmTree Master Analytics
echo "Starting ThePalmTree Master Analytics..."
cd /workspaces/freqtrade/user_data/analytics
# Run the analytics tool in a background process
nohup python thepalmtree_master_analytics.py start > /tmp/analytics.log 2>&1 &
ANALYTICS_PID=$!
echo "ThePalmTree Master Analytics started with PID: $ANALYTICS_PID"

echo ""
echo "=== All components started ==="
echo "Run 'zsh /workspaces/freqtrade/check_palmtree_flexible.sh' to verify status"
echo ""
echo "Simple Dashboard URL: http://localhost:8061"
echo "Main Dashboard URL: http://localhost:9090"
echo "FreqTrade API URL: http://localhost:8061"
echo "Login credentials:"
echo "Username: thepalmtree"
echo "Password: thepalmtree@55"
