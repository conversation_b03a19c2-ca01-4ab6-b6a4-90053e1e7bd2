#!/bin/bash

# Enhanced ThePalmTree Log Viewer with Neon Green Styling & Live Summary
# Provides organized, user-friendly log viewing experience with real-time stats

# ANSI color codes - enhanced palette
NEON_GREEN="\033[38;5;46m"
BRIGHT_GREEN="\033[1;32m"
CYAN="\033[1;36m"
YELLOW="\033[1;33m"
RED="\033[1;31m"
BLUE="\033[1;34m"
MAGENTA="\033[1;35m"
RESET="\033[0m"
BOLD="\033[1m"
DIM="\033[2m"
BLINK="\033[5m"

# Get current Chicago time
CHICAGO_TIME=$(TZ="America/Chicago" date +"%A %B %d, %Y %I:%M:%S %p")

# Function to get quick stats
get_quick_stats() {
    # Get live trade count from FreqTrade logs
    LIVE_TRADES=$(grep -c "open_trade" /tmp/freqtrade.log 2>/dev/null || echo "0")

    # Get basic P&L info (simplified)
    CURRENT_PNL=$(grep "profit" /tmp/freqtrade.log 2>/dev/null | tail -1 | grep -o '[+-][0-9]*\.[0-9]*%' | tail -1 || echo "0.00%")

    # System health check
    if pgrep -f "freqtrade" > /dev/null; then
        BOT_STATUS="${NEON_GREEN}●${RESET} ONLINE"
    else
        BOT_STATUS="${RED}●${RESET} OFFLINE"
    fi

    # Win rate (simplified calculation)
    WIN_COUNT=$(grep -c "ROI reached" /tmp/freqtrade.log 2>/dev/null || echo "0")
    TOTAL_COUNT=$(grep -c "SELL" /tmp/freqtrade.log 2>/dev/null || echo "1")
    WIN_RATE=$(echo "scale=1; $WIN_COUNT * 100 / $TOTAL_COUNT" | bc 2>/dev/null || echo "0.0")
}

# ASCII Art ThePalmTree Logo
display_logo() {
    echo -e "${NEON_GREEN}${BOLD}"
    echo "    🌴 ████████╗██╗  ██╗███████╗██████╗  █████╗ ██╗     ███╗   ███╗████████╗██████╗ ███████╗███████╗"
    echo "    🌴 ╚══██╔══╝██║  ██║██╔════╝██╔══██╗██╔══██╗██║     ████╗ ████║╚══██╔══╝██╔══██╗██╔════╝██╔════╝"
    echo "    🌴    ██║   ███████║█████╗  ██████╔╝███████║██║     ██╔████╔██║   ██║   ██████╔╝█████╗  █████╗  "
    echo "    🌴    ██║   ██╔══██║██╔══╝  ██╔═══╝ ██╔══██║██║     ██║╚██╔╝██║   ██║   ██╔══██╗██╔══╝  ██╔══╝  "
    echo "    🌴    ██║   ██║  ██║███████╗██║     ██║  ██║███████╗██║ ╚═╝ ██║   ██║   ██║  ██║███████╗███████╗"
    echo "    🌴    ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝     ╚═╝  ╚═╝╚══════╝╚═╝     ╚═╝   ╚═╝   ╚═╝  ╚═╝╚══════╝╚══════╝"
    echo -e "${RESET}"
}

# Display enhanced header with stats
display_header() {
    clear
    get_quick_stats

    echo -e "${NEON_GREEN}${BOLD}╔═══════════════════════════════════════════════════════════════════════════════╗${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║                    ${BLINK}🌴 ThePalmTree Trading System 🌴${RESET}${NEON_GREEN}${BOLD}                    ║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║                              Live Log Monitor                                ║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}╠═══════════════════════════════════════════════════════════════════════════════╣${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${BRIGHT_GREEN}📅 Started: ${CHICAGO_TIME}${RESET} ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}╠═══════════════════════════════════════════════════════════════════════════════╣${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${CYAN}📊 QUICK STATS${RESET}                                                          ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${YELLOW}🔄 Live Trades: ${LIVE_TRADES}${RESET}     ${BLUE}💰 Current P&L: ${CURRENT_PNL}${RESET}     ${MAGENTA}📈 Win Rate: ${WIN_RATE}%${RESET} ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${CYAN}🤖 Bot Status: ${BOT_STATUS}${RESET}                                                    ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}╠═══════════════════════════════════════════════════════════════════════════════╣${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${BRIGHT_GREEN}📁 MONITORING FILES${RESET}                                                     ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${NEON_GREEN}  🤖 FreqTrade Bot:     /tmp/freqtrade.log${RESET}                              ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${NEON_GREEN}  📊 Dashboard:         /tmp/dashboard.log${RESET}                              ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${NEON_GREEN}  📋 Master Logs:       /tmp/master_logs.log${RESET}                           ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${NEON_GREEN}  📈 Analytics:         /tmp/analytics.log${RESET}                             ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}╠═══════════════════════════════════════════════════════════════════════════════╣${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${BRIGHT_GREEN}⌨️  CONTROLS: [Ctrl+C] Exit  [F] Filter  [S] Stats  [C] Clear${RESET}              ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}╚═══════════════════════════════════════════════════════════════════════════════╝${RESET}"
    echo ""
}

display_header

# Function to format timestamps
format_timestamp() {
    local line="$1"
    # Extract timestamp and highlight it
    if [[ $line =~ ([0-9]{4}-[0-9]{2}-[0-9]{2}[[:space:]]+[0-9]{2}:[0-9]{2}:[0-9]{2}) ]]; then
        local timestamp="${BASH_REMATCH[1]}"
        line="${line/$timestamp/${DIM}[$timestamp]${RESET}}"
    fi
    echo "$line"
}

# Function to create boxed important messages
create_box() {
    local message="$1"
    local color="$2"
    echo -e "${color}┌─────────────────────────────────────────────────────────────────────────────┐${RESET}"
    echo -e "${color}│ $message${RESET}"
    echo -e "${color}└─────────────────────────────────────────────────────────────────────────────┘${RESET}"
}

# Enhanced log colorization with better formatting
colorize_logs() {
    local line_count=0
    while IFS= read -r line; do
        line_count=$((line_count + 1))

        # Format timestamp
        line=$(format_timestamp "$line")

        # Special handling for critical events (boxed)
        if [[ $line == *"Emergency exit"* ]]; then
            create_box "⚠️  EMERGENCY EXIT: $line" "$RED"
        elif [[ $line == *"Auto-closing"* ]]; then
            create_box "⏰ AUTO-CLOSURE: $line" "$YELLOW"
        elif [[ $line == *"Runner Level 3"* ]]; then
            create_box "🚀 RUNNER LEVEL 3: $line" "$NEON_GREEN"

        # Enhanced color coding for different log types
        elif [[ $line == *"ERROR"* ]]; then
            echo -e "${RED}${BOLD}❌ ERROR:${RESET} ${RED}$line${RESET}"
        elif [[ $line == *"WARNING"* ]]; then
            echo -e "${YELLOW}${BOLD}⚠️  WARNING:${RESET} ${YELLOW}$line${RESET}"
        elif [[ $line == *"ThePalmTree"* ]]; then
            echo -e "${NEON_GREEN}${BOLD}🌴 PALMTREE:${RESET} ${NEON_GREEN}$line${RESET}"
        elif [[ $line == *"Blocking stablecoin"* ]] || [[ $line == *"BLOCKED"* ]]; then
            echo -e "${RED}${BOLD}🚫 BLOCKED:${RESET} ${RED}$line${RESET}"
        elif [[ $line == *"Runner Level 1"* ]]; then
            echo -e "${BRIGHT_GREEN}${BOLD}🚀 RUNNER L1:${RESET} ${BRIGHT_GREEN}$line${RESET}"
        elif [[ $line == *"Runner Level 2"* ]]; then
            echo -e "${CYAN}${BOLD}🚀 RUNNER L2:${RESET} ${CYAN}$line${RESET}"
        elif [[ $line == *"Confirming"* ]] && [[ $line == *"entry"* ]]; then
            echo -e "${BRIGHT_GREEN}${BOLD}📈 ENTRY:${RESET} ${BRIGHT_GREEN}$line${RESET}"
        elif [[ $line == *"Confirming"* ]] && [[ $line == *"exit"* ]]; then
            echo -e "${BLUE}${BOLD}📉 EXIT:${RESET} ${BLUE}$line${RESET}"
        elif [[ $line == *"profit"* ]] && [[ $line == *"+"* ]]; then
            echo -e "${BRIGHT_GREEN}${BOLD}💰 PROFIT:${RESET} ${BRIGHT_GREEN}$line${RESET}"
        elif [[ $line == *"loss"* ]] && [[ $line == *"-"* ]]; then
            echo -e "${RED}${BOLD}💸 LOSS:${RESET} ${RED}$line${RESET}"
        elif [[ $line == *"INFO"* ]]; then
            echo -e "${CYAN}${BOLD}ℹ️  INFO:${RESET} ${DIM}$line${RESET}"
        elif [[ $line == *"DEBUG"* ]]; then
            echo -e "${MAGENTA}${BOLD}🔍 DEBUG:${RESET} ${DIM}$line${RESET}"
        else
            echo -e "${DIM}$line${RESET}"
        fi

        # Add separator every 10 lines for readability
        if (( line_count % 10 == 0 )); then
            echo -e "${DIM}────────────────────────────────────────────────────────────────────────────────${RESET}"
        fi
    done
}

# Function to handle interactive controls
handle_controls() {
    echo -e "${NEON_GREEN}${BOLD}╔═══════════════════════════════════════════════════════════════════════════════╗${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${BRIGHT_GREEN}🎮 INTERACTIVE CONTROLS ACTIVATED${RESET}                                          ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${YELLOW}Press [ENTER] to refresh stats, [q] to quit, or wait for live updates...${RESET}     ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}╚═══════════════════════════════════════════════════════════════════════════════╝${RESET}"
    echo ""
}

# Function to show live updates notification
show_live_indicator() {
    echo -e "${NEON_GREEN}${BOLD}╔═══════════════════════════════════════════════════════════════════════════════╗${RESET}"
    echo -e "${NEON_GREEN}${BOLD}║${RESET} ${BLINK}🔴 LIVE${RESET} ${BRIGHT_GREEN}Monitoring active... New logs will appear below${RESET}                    ${NEON_GREEN}${BOLD}║${RESET}"
    echo -e "${NEON_GREEN}${BOLD}╚═══════════════════════════════════════════════════════════════════════════════╝${RESET}"
    echo ""
}

# Add live indicator
show_live_indicator

# Start monitoring logs with enhanced formatting
tail -f /tmp/freqtrade.log /tmp/dashboard.log /tmp/master_logs.log /tmp/analytics.log 2>/dev/null | colorize_logs
