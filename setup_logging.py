#!/usr/bin/env python3
"""
This script modifies the Python logging format to use 12-hour time with AM/PM.
It should be imported before any other imports in FreqTrade.
"""

import logging
import sys
from logging import Formatter

# Define a custom formatter with 12-hour time format
class CustomFormatter(Formatter):
    def formatTime(self, record, datefmt=None):
        if datefmt:
            # Use the specified format
            return Formatter.formatTime(self, record, datefmt)
        else:
            # Use our custom format (12-hour with AM/PM)
            datefmt = '%Y-%m-%d %I:%M:%S %p'
            return Formatter.formatTime(self, record, datefmt)

# Apply the custom formatter to the root logger
root_logger = logging.getLogger()
for handler in root_logger.handlers:
    if isinstance(handler, logging.StreamHandler):
        handler.setFormatter(CustomFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

# Print a message to confirm the script has run
print("Logging format set to 12-hour time with AM/PM")
