#!/usr/bin/env python3
"""
This script directly patches the Python logging module to use 12-hour time format.
"""

import logging
import sys

# Store the original formatTime method
original_formatTime = logging.Formatter.formatTime

# Define our patched formatTime method
def patched_formatTime(self, record, datefmt=None):
    if datefmt:
        return original_formatTime(self, record, datefmt)
    else:
        # Always use 12-hour format with AM/PM
        return original_formatTime(self, record, '%Y-%m-%d %I:%M:%S %p')

# Apply the patch
logging.Formatter.formatTime = patched_formatTime

# Print confirmation
print("Logging time format patched to use 12-hour format with AM/PM")
