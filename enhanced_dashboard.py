#!/usr/bin/env python3
"""
ThePalmTree Trader Dashboard - Enhanced Server
Serves templates with live data integration and ThePalmTree stats
"""
import os
import time
import json
import base64
import logging
import subprocess
import http.server
import socketserver
from urllib.parse import parse_qs, urlparse
from datetime import datetime, timedelta
import sqlite3

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ThePalmTreeDashboard')

# Dashboard configuration
PORT = 9090
DIRECTORY = os.path.dirname(os.path.abspath(__file__))
USERNAME = "thepalmtree"
PASSWORD = "thepalmtree@55"
SESSION_TIMEOUT = 3600  # 1 hour

# Store active sessions
active_sessions = {}

class ThePalmTreeStatsAPI:
    """API class to fetch ThePalmTree trading statistics"""

    def __init__(self):
        self.freqtrade_db = "/workspaces/freqtrade/tradesv3.dryrun.sqlite"

    def run_palmtree_command(self, command):
        """Execute ThePalmTree alias commands"""
        try:
            # Source the aliases and run command
            cmd = f"source /home/<USER>/.devcontainer/thepalmtree_aliases.zsh && {command}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, executable='/bin/zsh')
            return result.stdout if result.returncode == 0 else None
        except Exception as e:
            logger.error(f"Error running command {command}: {e}")
            return None

    def get_daily_stats(self):
        """Get today's trading statistics"""
        try:
            output = self.run_palmtree_command("thepalmtree-journal-today")
            if output:
                return self.parse_journal_output(output, "today")
            return {"error": "No data available"}
        except Exception as e:
            logger.error(f"Error getting daily stats: {e}")
            return {"error": str(e)}

    def get_monthly_stats(self):
        """Get monthly trading statistics"""
        try:
            output = self.run_palmtree_command("thepalmtree-monthly-trades")
            if output:
                return self.parse_journal_output(output, "monthly")
            return {"error": "No data available"}
        except Exception as e:
            logger.error(f"Error getting monthly stats: {e}")
            return {"error": str(e)}

    def get_yesterday_stats(self):
        """Get yesterday's trading statistics"""
        try:
            output = self.run_palmtree_command("thepalmtree-journal-yesterday")
            if output:
                return self.parse_journal_output(output, "yesterday")
            return {"error": "No data available"}
        except Exception as e:
            logger.error(f"Error getting yesterday stats: {e}")
            return {"error": str(e)}

    def parse_journal_output(self, output, period):
        """Parse journal command output into structured data"""
        try:
            stats = {
                "period": period,
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "total_profit": 0.0,
                "avg_profit": 0.0,
                "avg_loss": 0.0,
                "profit_factor": 0.0,
                "max_drawdown": 0.0,
                "sharpe_ratio": 0.0,
                "timestamp": datetime.now().isoformat()
            }

            lines = output.split('\n')
            for line in lines:
                line = line.strip()
                if 'Total Trades:' in line:
                    stats["total_trades"] = int(line.split(':')[1].strip())
                elif 'Win Rate:' in line:
                    stats["win_rate"] = float(line.split(':')[1].strip().replace('%', ''))
                elif 'Total Profit:' in line:
                    stats["total_profit"] = float(line.split(':')[1].strip().replace('%', ''))
                elif 'Average Profit:' in line:
                    stats["avg_profit"] = float(line.split(':')[1].strip().replace('%', ''))
                elif 'Average Loss:' in line:
                    stats["avg_loss"] = float(line.split(':')[1].strip().replace('%', ''))
                elif 'Profit Factor:' in line:
                    stats["profit_factor"] = float(line.split(':')[1].strip())
                elif 'Max Drawdown:' in line:
                    stats["max_drawdown"] = float(line.split(':')[1].strip().replace('%', ''))
                elif 'Sharpe Ratio:' in line:
                    stats["sharpe_ratio"] = float(line.split(':')[1].strip())

            return stats
        except Exception as e:
            logger.error(f"Error parsing journal output: {e}")
            return {"error": "Failed to parse stats"}

    def get_bybit_balance(self):
        """Get Bybit account balance using FreqTrade API"""
        try:
            # Try to get balance from FreqTrade database
            if os.path.exists(self.freqtrade_db):
                conn = sqlite3.connect(self.freqtrade_db)
                cursor = conn.cursor()

                # Get latest balance info (simplified)
                cursor.execute("SELECT COUNT(*) as open_trades FROM trades WHERE is_open = 1")
                open_trades = cursor.fetchone()[0]

                conn.close()

                return {
                    "dry_run_balance": 1000.0,  # Default dry run balance
                    "live_balance": "API_CONNECTED",  # Placeholder for live balance
                    "open_trades": open_trades,
                    "currency": "USDT",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"error": "Database not found"}
        except Exception as e:
            logger.error(f"Error getting Bybit balance: {e}")
            return {"error": str(e)}

# Initialize stats API
stats_api = ThePalmTreeStatsAPI()

class ThePalmTreeDashboardHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)

    def do_GET(self):
        logger.info(f"GET request for path: {self.path}")

        # Parse URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        # API endpoints
        if path.startswith('/api/'):
            self.handle_api_request(path)
            return

        # For the root path, check authentication and serve appropriate page
        if path == '/' or path == '/index.html':
            if self.is_authenticated():
                # Serve the index.html template
                self.serve_file('/templates/index.html')
            else:
                # Serve the login.html template
                self.serve_file('/templates/login.html')
            return

        # Handle logout
        if path == '/logout':
            self.handle_logout()
            return

        # For other paths, serve static files if authenticated
        if self.is_authenticated():
            super().do_GET()
        else:
            self.serve_file('/templates/login.html')

    def handle_api_request(self, path):
        """Handle API requests for live data"""
        try:
            if path == '/api/stats/daily':
                data = stats_api.get_daily_stats()
            elif path == '/api/stats/monthly':
                data = stats_api.get_monthly_stats()
            elif path == '/api/stats/yesterday':
                data = stats_api.get_yesterday_stats()
            elif path == '/api/balance':
                data = stats_api.get_bybit_balance()
            else:
                data = {"error": "Unknown API endpoint"}

            # Send JSON response
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(data).encode())

        except Exception as e:
            logger.error(f"API error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())

    def do_POST(self):
        """Handle POST requests for login"""
        if self.path == '/login':
            self.handle_login()
        else:
            self.send_error(404)

    def handle_login(self):
        """Handle login authentication"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length).decode('utf-8')
            params = parse_qs(post_data)

            username = params.get('username', [''])[0]
            password = params.get('password', [''])[0]

            if username == USERNAME and password == PASSWORD:
                # Create session
                session_token = base64.b64encode(f"{username}:{time.time()}".encode()).decode()
                active_sessions[session_token] = {
                    'username': username,
                    'created': time.time(),
                    'expires': time.time() + SESSION_TIMEOUT
                }

                # Redirect to dashboard
                self.send_response(302)
                self.send_header('Location', '/')
                self.send_header('Set-Cookie', f'session={session_token}; Path=/; Max-Age={SESSION_TIMEOUT}')
                self.end_headers()
                logger.info(f"User {username} logged in successfully")
            else:
                # Invalid credentials
                self.serve_file('/templates/login.html', error=True)
                logger.warning(f"Failed login attempt for user: {username}")
        except Exception as e:
            logger.error(f"Login error: {e}")
            self.send_error(500)

    def handle_logout(self):
        """Handle logout"""
        # Get session token
        cookies = {}
        cookie_header = self.headers.get('Cookie', '')
        for cookie in cookie_header.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookies[name] = value

        session_token = cookies.get('session')
        if session_token and session_token in active_sessions:
            del active_sessions[session_token]

        # Redirect to login
        self.send_response(302)
        self.send_header('Location', '/')
        self.send_header('Set-Cookie', 'session=; Path=/; Max-Age=0')
        self.end_headers()
        logger.info("User logged out")

    def is_authenticated(self):
        """Check if user is authenticated"""
        # Get cookies
        cookies = {}
        cookie_header = self.headers.get('Cookie', '')
        for cookie in cookie_header.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookies[name] = value

        # Check session
        session_token = cookies.get('session')
        if not session_token or session_token not in active_sessions:
            return False

        # Check if session is expired
        session = active_sessions[session_token]
        if session['expires'] < time.time():
            del active_sessions[session_token]
            return False

        # Extend session
        session['expires'] = time.time() + SESSION_TIMEOUT
        return True

    def serve_file(self, file_path, error=False):
        """Serve a file from the filesystem"""
        try:
            # Get the full path
            full_path = os.path.join(DIRECTORY, file_path.lstrip('/'))
            logger.info(f"Serving file from: {full_path}")

            # Read the file
            with open(full_path, 'r') as f:
                content = f.read()

            # Add error message if needed
            if error and file_path.endswith('login.html'):
                content = content.replace('<!--ERROR_MESSAGE-->', '<div class="error-message">Invalid username or password</div>')

            # Send the response
            self.send_response(200)
            self.send_header('Content-Type', 'text/html')
            self.end_headers()
            self.wfile.write(content.encode())
            logger.info(f"Served file: {file_path}")
        except Exception as e:
            logger.error(f"Error serving file {file_path}: {e}")
            self.send_error(500, f"Error: {e}")

if __name__ == "__main__":
    logger.info(f"Starting Enhanced ThePalmTree Trader Dashboard on port {PORT}")

    with socketserver.TCPServer(("", PORT), ThePalmTreeDashboardHandler) as httpd:
        logger.info(f"Enhanced server running at http://localhost:{PORT}")
        logger.info("New API endpoints available:")
        logger.info("  /api/stats/daily - Today's trading stats")
        logger.info("  /api/stats/monthly - Monthly trading stats")
        logger.info("  /api/stats/yesterday - Yesterday's trading stats")
        logger.info("  /api/balance - Bybit balance information")
        httpd.serve_forever()
