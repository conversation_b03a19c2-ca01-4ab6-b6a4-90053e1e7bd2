#!/bin/bash

# ANSI color codes
GREEN="\033[92m"
YELLOW="\033[93m"
RED="\033[91m"
BLUE="\033[94m"
MAGENTA="\033[95m"
CYAN="\033[96m"
RESET="\033[0m"
BOLD="\033[1m"

# Get current Chicago time
CHICAGO_TIME=$(TZ="America/Chicago" date +"%I:%M:%S %p")
CHICAGO_DATE=$(TZ="America/Chicago" date +"%A %B %d, %Y")

echo -e "${GREEN}${BOLD}Starting ThePalmTree Trading Bot - ${CHICAGO_TIME} Chicago Time${RESET}"
echo -e "${GREEN}${BOLD}Date: ${CHICAGO_DATE}${RESET}"
echo -e "${GREEN}${BOLD}===============================================================${RESET}"

# Create log directories
mkdir -p /workspaces/freqtrade/user_data/logs/journal

# Start FreqTrade with ThePalmTreeMasterStrategy
echo -e "${GREEN}Starting FreqTrade with ThePalmTreeMasterStrategy...${RESET}"
cd /workspaces/freqtrade
# Run FreqTrade in a background process with Chicago timezone
nohup /home/<USER>/.devcontainer/run_chicago_time.sh trade --strategy ThePalmTreeMasterStrategy > /tmp/freqtrade.log 2>&1 &
FREQTRADE_PID=$!
echo -e "${GREEN}FreqTrade started with PID: ${FREQTRADE_PID}${RESET}"

# Start the session monitor
echo -e "${GREEN}Starting Session Monitor...${RESET}"
nohup /workspaces/freqtrade/user_data/scripts/session_monitor.sh > /tmp/session_monitor.log 2>&1 &
SESSION_MONITOR_PID=$!
echo -e "${GREEN}Session Monitor started with PID: ${SESSION_MONITOR_PID}${RESET}"

# Start the dashboard
echo -e "${GREEN}Starting ThePalmTree Dashboard...${RESET}"
cd /workspaces/freqtrade/user_data/thepalmtree_dashboard
nohup python simple_dashboard.py > /tmp/dashboard.log 2>&1 &
DASHBOARD_PID=$!
echo -e "${GREEN}Dashboard started with PID: ${DASHBOARD_PID}${RESET}"

# Generate initial trade journal entry
echo -e "${GREEN}Generating initial trade journal entry...${RESET}"
cd /workspaces/freqtrade
python user_data/scripts/trade_journal.py

echo -e "${GREEN}${BOLD}All ThePalmTree components started successfully!${RESET}"
echo -e "${GREEN}${BOLD}===============================================================${RESET}"
echo -e "${GREEN}Use 'thepalmtree-status' to check the status of all components${RESET}"
echo -e "${GREEN}Use 'thepalmtree-view-logs' to view all logs in real-time${RESET}"
echo -e "${GREEN}Use 'thepalmtree-view-journal' to view the trading journal${RESET}"
